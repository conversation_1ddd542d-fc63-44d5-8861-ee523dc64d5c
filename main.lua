--[[
模块：main.lua（游戏入口与状态管理）
职责：
- 作为 Love2D 程序的入口文件，加载引擎与全部游戏层模块
- 初始化全局变量（角色颜色、职业颜色、新游戏+相关变量等）
- 完成输入绑定、资源加载调用、随机音乐播放初始化
- 创建主状态机，注册MainMenu状态并跳转到主菜单（其他状态按需添加）
- 顶层 update/draw 委托给当前状态；处理窗口缩放控制
- 负责与引擎 engine_run 连接（love.run），设定窗口策略
- 提供调试内存监控功能
关键点：
- 模块化：通过 game/ 目录下的模块化文件管理各类游戏数据
- 状态机：通过 main:add(State'id') 注册状态，main:go_to('id', ...) 进行切换
- 延迟初始化：颜色映射需在 shared_init() 后初始化以确保依赖关系正确
- 数据持久化：在合适时机调用 system.save_state()/system.save_run()，分别写入 state.txt、run_v4.txt
- 性能：顶层 update/draw 仅做委托；避免在循环中新建临时表或闭包
依赖：
- engine（引擎层）、game（游戏层聚合模块，包含所有场景和数据模块）
]]--

require 'engine'
require 'game'

-- 功能：游戏主初始化，绑定输入、加载资源、初始化音乐与状态
-- 参数：无
-- 返回：无
function init()

  -- 初始化全局颜色主题、字体、画布与阴影着色器
  shared_init()

  -- 初始化角色颜色数据（必须在shared_init之后）
  init_character_colors()
  
  -- 初始化职业颜色数据
  init_class_colors()

  -- 初始化新游戏+相关变量
  new_game_plus = state.new_game_plus or 0
  if not state.new_game_plus then state.new_game_plus = new_game_plus end
  current_new_game_plus = state.current_new_game_plus or new_game_plus
  if not state.current_new_game_plus then state.current_new_game_plus = current_new_game_plus end
  max_units = math.clamp(7 + current_new_game_plus, 7, 12)

  -- 输入绑定
  input:bind('move_left', {'a', 'left', 'dpleft', 'm1'})
  input:bind('move_right', {'d', 'e', 's', 'right', 'dpright', 'm2'})
  input:bind('enter', {'space', 'return', 'fleft', 'fdown', 'fright'})

  -- 加载所有资源（音效和图像）
  load_resources()

  -- 初始化音乐（随机选择）
  main_song_instance = _G[random:table{'song1', 'song2', 'song3', 'song4', 'song5'}]:play{volume = 0.5}

  -- 状态机初始化
  main = Main()
  main:add(MainMenu'mainmenu')

  -- 跳转到主菜单
  main:go_to('mainmenu')

  -- 设置初始状态变量
  slow_amount = 1
  music_slow_amount = 1
  transitioning = false
  debugging_memory = false

  -- 调试内存监控功能
  trigger:every(2, function()
    if debugging_memory then
      for k, v in pairs(system.type_count()) do
        print(k, v)
      end
      print("-- " .. math.round(tonumber(collectgarbage("count"))/1024, 3) .. "MB --")
      print()
    end
  end)

end

-- 功能：主更新循环，委托给当前状态并处理全局快捷键
-- 参数：dt - 帧时间间隔
-- 返回：无
function update(dt)
  main:update(dt)

  -- 窗口缩放控制
  if input.k.pressed then
    if sx > 1 and sy > 1 then
      sx, sy = sx - 0.5, sy - 0.5
      love.window.setMode(480*sx, 270*sy)
      state.sx, state.sy = sx, sy
      state.fullscreen = false
    end
  end

  if input.l.pressed then
    sx, sy = sx + 0.5, sy + 0.5
    love.window.setMode(480*sx, 270*sy)
    state.sx, state.sy = sx, sy
    state.fullscreen = false
  end
end

-- 功能：主绘制循环，使用 shared_draw 统一背景/主画布/阴影绘制管线
-- 参数：无
-- 返回：无
function draw()
  shared_draw(function()
    main:draw()
  end)
end

-- 功能：Love2D 主循环配置
-- 参数：无
-- 返回：engine_run 函数调用结果
function love.run()
  return engine_run({
    game_name = 'XRKNS',
    window_width = 'max',
    window_height = 'max',
  })
end