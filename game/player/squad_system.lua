--[[
模块：squad_system.lua
职责：队伍管理系统，包括单位获取、领队选择、跟随者管理等
目的：将队伍管理逻辑从player.lua分离，提高代码组织性
]]--

-- 队伍管理系统模块
local SquadSystem = {}

-- 功能：获取所有队伍成员（领队+跟随者）
-- 参数：self - Player实例
-- 返回：table - 包含所有单位的数组
function SquadSystem.get_all_units(self)
  local followers
  local leader = (self.leader and self) or self.parent
  if self.leader then 
    followers = self.followers 
  else 
    followers = self.parent.followers 
  end
  return {leader, unpack(followers)}
end

-- 功能：获取队伍领队
-- 参数：self - Player实例
-- 返回：Player - 领队单位
function SquadSystem.get_leader(self)
  return (self.leader and self) or self.parent
end

-- 功能：按角色标识从编队中获取对应单位
-- 参数：self - Player实例, character - 角色名字符串
-- 返回：Player|nil - 命中则返回单位，否则返回nil
function SquadSystem.get_unit(self, character)
  local all_units = SquadSystem.get_all_units(self)
  for _, unit in ipairs(all_units) do
    if unit.character == character then 
      return unit 
    end
  end
  return nil
end

-- 功能：添加跟随者到队伍
-- 参数：self - Player实例（领队）, unit - 要添加的单位
-- 返回：无
function SquadSystem.add_follower(self, unit)
  if not self.leader then
    error("只有领队才能添加跟随者")
    return
  end
  
  table.insert(self.followers, unit)
  unit.parent = self
  unit.follower_index = #self.followers
  unit.leader = false
end

-- 功能：当领队死亡或成员阵亡时，重算队列；必要时选出新领队并重建跟随关系
-- 参数：self - Player实例
-- 返回：无
function SquadSystem.recalculate_followers(self)
  if not self.leader then
    error("只有领队才能重计算跟随者")
    return
  end
  
  if self.dead then
    -- 领队死亡，选出新领队
    if #self.followers > 0 then
      local new_leader = table.remove(self.followers, 1)
      new_leader.leader = true
      new_leader.previous_positions = {}
      new_leader.followers = self.followers
      
      -- 设置新领队的位置追踪
      new_leader.t:every(0.01, function()
        table.insert(new_leader.previous_positions, 1, {x = new_leader.x, y = new_leader.y, r = new_leader.r})
        if #new_leader.previous_positions > 256 then 
          new_leader.previous_positions[257] = nil 
        end
      end)
      
      -- 更新游戏状态中的玩家引用
      if main and main.current then
        main.current.player = new_leader
      end
      
      -- 更新所有跟随者的父级引用和索引
      for i, follower in ipairs(new_leader.followers) do
        follower.parent = new_leader
        follower.follower_index = i
      end
    end
  else
    -- 领队存活，移除死亡的跟随者并重新索引
    for i = #self.followers, 1, -1 do
      if self.followers[i].dead then
        table.remove(self.followers, i)
        break
      end
    end
    
    -- 重新计算跟随者索引
    for i, follower in ipairs(self.followers) do
      follower.follower_index = i
    end
  end
end

-- 功能：获取队伍总数量
-- 参数：self - Player实例
-- 返回：number - 队伍总人数
function SquadSystem.get_squad_size(self)
  local all_units = SquadSystem.get_all_units(self)
  return #all_units
end

-- 功能：检查队伍中是否有特定角色
-- 参数：self - Player实例, character - 角色名字符串
-- 返回：boolean - 是否存在该角色
function SquadSystem.has_character(self, character)
  return SquadSystem.get_unit(self, character) ~= nil
end

-- 功能：获取队伍中所有存活的单位
-- 参数：self - Player实例
-- 返回：table - 存活单位数组
function SquadSystem.get_alive_units(self)
  local all_units = SquadSystem.get_all_units(self)
  local alive_units = {}
  
  for _, unit in ipairs(all_units) do
    if not unit.dead then
      table.insert(alive_units, unit)
    end
  end
  
  return alive_units
end

-- 功能：获取队伍中指定职业的所有单位
-- 参数：self - Player实例, class_name - 职业名称
-- 返回：table - 该职业的所有单位数组
function SquadSystem.get_units_by_class(self, class_name)
  local all_units = SquadSystem.get_all_units(self)
  local class_units = {}
  
  for _, unit in ipairs(all_units) do
    if unit.classes and table.any(unit.classes, function(v) return v == class_name end) then
      table.insert(class_units, unit)
    end
  end
  
  return class_units
end

-- 功能：移除特定跟随者（不重新计算索引，用于特殊情况）
-- 参数：self - Player实例（领队）, unit - 要移除的单位
-- 返回：boolean - 是否成功移除
function SquadSystem.remove_follower(self, unit)
  if not self.leader then
    return false
  end
  
  for i, follower in ipairs(self.followers) do
    if follower == unit then
      table.remove(self.followers, i)
      unit.parent = nil
      unit.follower_index = nil
      
      -- 重新计算剩余跟随者的索引
      for j, remaining_follower in ipairs(self.followers) do
        remaining_follower.follower_index = j
      end
      
      return true
    end
  end
  
  return false
end

-- 导出模块
return SquadSystem