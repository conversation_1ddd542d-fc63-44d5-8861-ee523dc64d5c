--[[
模块：combat_system.lua  
职责：战斗系统相关功能，包括攻击、受击、治疗、投射物等
目的：将战斗逻辑从player.lua分离，提高代码组织性
]]--

local constants = require('game.player.player_constants')

-- 战斗系统模块
local CombatSystem = {}

-- 功能：执行区域攻击
-- 参数：self - Player实例, area - 攻击范围, mods - 攻击修饰符
-- 返回：无
function CombatSystem.attack(self, area, mods)
  mods = mods or {}
  camera:shake(2, 0.5)
  
  local t = {group = main.current.effects, x = mods.x or self.x, y = mods.y or self.y, r = self.r, w = self.area_size_m*(area or 64), 
    color = self.color, dmg = self.area_dmg_m*self.dmg*(self.attack_dmg_m or 1), character = self.character, level = self.level, 
    parent = self, void_rift = self.void_rift, echo_barrage = self.echo_barrage}
  
  if mods.spawn_critters_on_hit then 
    t.spawn_critters_on_hit = mods.spawn_critters_on_hit 
  end
  
  if mods.spawn_critters_on_kill then 
    t.spawn_critters_on_kill = mods.spawn_critters_on_kill 
  end
  
  Area(t)
  
  if mods.spawn_critters_on_hit then
    critter1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
  end
  
  _G[random:table{'attack1', 'attack2', 'attack3'}]:play{pitch = random:float(0.95, 1.05), volume = 0.2}
  
  if mods.stun then
    local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
    for _, enemy in ipairs(enemies) do
      enemy:slow(0.25, mods.stun)
    end
  end
  
  if self.character == 'elementor' then
    self.elementor_dmg = self.dmg
  end
  
  if self.character == 'psychic' then
    self.sorcerer_count = self.sorcerer_count or 0
  end
end

-- 功能：发射投射物
-- 参数：self - Player实例, r - 发射角度, mods - 投射物修饰符
-- 返回：无
function CombatSystem.shoot(self, r, mods)
  mods = mods or {}
  camera:shake(2, 0.5)
  self.hfx:use('flash', 0.25)
  
  local dmg_m = (self.crit and mods.crit_dmg_m or 1)
  local dmg = dmg_m*self.dmg*(self.ranger_dmg_m or 1)*(self.attack_dmg_m or 1)
  
  if self.character == 'blade' or self.character == 'spellblade' then
    local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
    if enemies and #enemies > 0 then
      for _, enemy in ipairs(enemies) do
        local r = self:angle_to_object(enemy)
        Projectile{
          group = main.current.main, 
          x = self.x + 8*math.cos(r), 
          y = self.y + 8*math.sin(r), 
          v = 500, 
          r = r, 
          color = self.color,
          dmg = dmg, 
          parent = self, 
          character = 'blade',
          level = self.level, 
          pierce = mods.pierce or 0, 
          chain = mods.chain or 0, 
          ricochet = mods.ricochet or 0,
          shoot_5 = mods.shoot_5, 
          shoot_2 = mods.shoot_2, 
          homing = mods.homing,
          stop_on_hit = mods.stop_on_hit
        }
      end
    end
  else
    Projectile{
      group = main.current.main, 
      x = self.x + 8*math.cos(r), 
      y = self.y + 8*math.sin(r), 
      v = mods.v or 300, 
      r = r, 
      color = self.color,
      dmg = dmg, 
      parent = self, 
      character = self.character,
      level = self.level, 
      pierce = mods.pierce or 0, 
      chain = mods.chain or 0, 
      ricochet = mods.ricochet or 0,
      shoot_5 = mods.shoot_5, 
      shoot_2 = mods.shoot_2, 
      homing = mods.homing,
      speed_m = mods.speed_m or 1, 
      range_m = mods.range_m or 1,
      crit = self.crit, 
      stun = mods.stun,
      -- 处理特殊效果
      spawn_critters_on_hit = mods.spawn_critters_on_hit, 
      spawn_critters_on_kill = mods.spawn_critters_on_kill,
      spawn_critters_on_crit = (self.crit and mods.spawn_critters_on_crit),
      void_rift = self.void_rift, 
      echo_barrage = self.echo_barrage,
      knockback = mods.knockback, 
      blunt_arrow = mods.blunt_arrow, 
      split = mods.split,
      scatter = mods.scatter, 
      burn = mods.burn, 
      slow = mods.slow, 
      freeze = mods.freeze, 
      bubble = mods.bubble,
      chain_on_crit = mods.chain_on_crit, 
      chain_on_kill = mods.chain_on_kill,
      instance_travel = mods.instance_travel, 
      bounce = mods.bounce,
      speed_increase = mods.speed_increase
    }
  end
  
  -- 播放音效
  if self.character == 'sage' or self.character == 'hunter' then
    shoot3:play{pitch = random:float(0.95, 1.05), volume = 0.2}
  elseif self.character == 'spellblade' then
    _G[random:table{'sword1', 'sword2', 'sword3'}]:play{pitch = random:float(0.9, 1.1), volume = 0.25}
  elseif self.character == 'dual_gunner' then
    dual_gunner1:play{pitch = random:float(0.9, 1.1), volume = 0.3}
  elseif self.character == 'cannoneer' then
    _G[random:table{'cannoneer1', 'cannoneer2'}]:play{pitch = random:float(0.95, 1.05), volume = 0.5}
  else
    shoot1:play{pitch = random:float(0.95, 1.05), volume = 0.1}
  end
end

-- 功能：弹幕攻击
-- 参数：self - Player实例, r - 角度, n - 数量, pierce - 穿透, ricochet - 弹射, shoot_5 - 额外投射, homing - 追踪
-- 返回：无
function CombatSystem.barrage(self, r, n, pierce, ricochet, shoot_5, homing)
  n = n or 8
  
  -- 特殊角色处理
  if self.character == 'blade' or self.character == 'spellblade' then
    local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
    if enemies and #enemies > 0 then
      for _, enemy in ipairs(enemies) do
        local r = self:angle_to_object(enemy)
        for i = 1, n do
          self.t:after((i-1)*0.05, function()
            self.hfx:use('flash', 0.25)
            Projectile{
              group = main.current.main, 
              x = self.x + 8*math.cos(r), 
              y = self.y + 8*math.sin(r), 
              v = 500, 
              r = r,
              color = self.color, 
              dmg = self.dmg*self.barrage_dmg_m, 
              parent = self,
              character = 'blade', 
              level = self.level, 
              pierce = pierce or 0, 
              ricochet = ricochet or 0, 
              shoot_5 = shoot_5, 
              homing = homing,
              stop_on_hit = self.barrage_stop_on_hit
            }
            _G[random:table{'sword1', 'sword2', 'sword3'}]:play{pitch = random:float(0.9, 1.1), volume = 0.1}
          end)
        end
      end
    end
  else
    for i = 1, n do
      self.t:after((i-1)*0.05, function()
        self.hfx:use('flash', 0.25)
        Projectile{
          group = main.current.main, 
          x = self.x + 8*math.cos(r), 
          y = self.y + 8*math.sin(r), 
          v = 500, 
          r = r + random:float(-math.pi/16, math.pi/16),
          color = self.color, 
          dmg = self.dmg*self.barrage_dmg_m, 
          parent = self,
          character = self.character, 
          level = self.level, 
          pierce = pierce or 0, 
          ricochet = ricochet or 0, 
          shoot_5 = shoot_5, 
          homing = homing,
          stop_on_hit = self.barrage_stop_on_hit
        }
        shoot1:play{pitch = random:float(0.95, 1.05), volume = 0.05}
      end)
    end
  end
end

-- 功能：受击处理
-- 参数：self - Player实例, damage - 伤害值, from_undead - 是否来自亡灵
-- 返回：无
function CombatSystem.hit(self, damage, from_undead)
  if self.dead then return end
  if self.magician_invulnerable then return end
  if self.undead and not from_undead then return end
  
  damage = math.max(damage - (self.def or 0), 1)
  
  -- 爆击承伤加成
  local crit_taken_dmg_m = 1
  if main.current.player.critical_strike and not from_undead then
    crit_taken_dmg_m = crit_taken_dmg_m*(1 + 0.05*main.current.player.critical_strike)
  end
  damage = damage*crit_taken_dmg_m
  
  -- 防御力修正
  damage = damage/(self.defensive_stance_def_m or 1)/(self.unwavering_stance_def_m or 1)
  
  if self.amplify and not from_undead then
    if random:bool((self.amplify == 1 and 10) or (self.amplify == 2 and 20) or (self.amplify == 3 and 30) or 0) then
      damage = damage*2
      self.hfx:use('hit', 0.5, 'up')
      camera:shake(5, 0.25)
      rogue_crit1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
      for i = 1, 2 do
        self.t:after(i*0.1, function()
          Area{group = main.current.effects, x = self.x, y = self.y, w = self.area_size_m*64, r = self.r, 
            color = self.color, character = self.character, parent = self,
            dmg = (self.area_dmg_m or 1)*self.dmg*(self.amplify_area_dmg_m or 1)*(self.amplify_dmg_m or 1),
            level = self.level, void_rift = self.void_rift, echo_barrage = self.echo_barrage}
        end)
      end
    else
      self.hfx:use('hit', 0.25, 'down')
      hit2:play{pitch = random:float(0.95, 1.05), volume = 0.4}
    end
  else
    self.hfx:use('hit', 0.25, 'down')
    hit2:play{pitch = random:float(0.95, 1.05), volume = 0.4}
  end
  
  -- 亡灵检测
  local actual_damage = self:calculate_damage(damage)
  if self.dead then return end
  
  if actual_damage >= self.hp then
    hit4:play{pitch = random:float(0.95, 1.05), volume = 0.5}
    for i = 1, 3 do
      HitParticle{group = main.current.effects, x = self.x, y = self.y, color = self.color}
    end
    HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 12}
    if self.divined then
      self.divined = false
      self.hp = 0
      self:heal(self.max_hp)
    else
      slow(0.25, 1)
      self.dead = true
      self.hp = 0
    end
  else
    self.hp = self.hp - actual_damage
  end
  
  self:show_hp()
end

-- 功能：计算实际伤害
-- 参数：self - Player实例, damage - 原始伤害值
-- 返回：实际伤害值
function CombatSystem.calculate_damage(self, damage)
  local actual_damage = damage
  
  -- 血量触发被动
  if self.baneling_burst and self.hp - actual_damage <= 0 then
    if (table.any(main.current.enemies, function(v) return self:distance_to_object(v) < 96 end)) then
      camera:shake(5, 0.5)
      ban3:play{pitch = random:float(0.9, 1.1), volume = 0.75}
      for i = 1, (self.baneling_burst == 3 and 6 or 3) do
        self.t:after((i-1)*0.1, function()
          Area{group = main.current.effects, x = self.x, y = self.y, w = (self.baneling_burst == 3 and 250 or 150)*self.area_size_m, color = self.color, 
            dmg = ((self.baneling_burst == 1 and 1.5) or (self.baneling_burst == 2 and 2.0) or (self.baneling_burst == 3 and 2.5) or 0)*self.max_hp, parent = self}
        end)
      end
      actual_damage = actual_damage - (actual_damage + self.hp - 1)
    end
  end
  
  if self.divined then
    actual_damage = math.min(actual_damage, self.hp - 1)
  end
  
  return actual_damage
end

-- 功能：治疗处理
-- 参数：self - Player实例, amount - 治疗量
-- 返回：无
function CombatSystem.heal(self, amount)
  local hp_before = self.hp
  
  self.hfx:use('hit', 0.25, 200, 10)
  self:show_hp(1.5)
  self:show_heal(1.5)
  self.hp = self.hp + amount
  if self.hp > self.max_hp then self.hp = self.max_hp end
  
  if self.character_hp then
    self.character_hp:change_hp()
  end
end

-- 功能：碰撞进入处理
-- 参数：self - Player实例, other - 碰撞对象, contact - 碰撞接触点
-- 返回：无
function CombatSystem.on_collision_enter(self, other, contact)
  if table.any(main.current.enemies, function(v) return other:is(v) end) then
    other:push(random:float(15, 20)*(self.knockback_m or 1), self:angle_to_object(other))
  end
end

-- 功能：触发器进入处理
-- 参数：self - Player实例, other - 触发对象, contact - 触发接触点
-- 返回：无
function CombatSystem.on_trigger_enter(self, other, contact)
  if table.any(main.current.enemies, function(v) return other:is(v) end) then
    other:hit(0)
    other:push(random:float(15, 20)*(self.knockback_m or 1), self:angle_to_object(other))
  end
  
  if other:is(EnemyProjectile) then
    if self.psykeeper then
      other.r = other.r - math.pi
      other.parent = self
      other:set_as_player_projectile()
    end
  end
  
  if other:is(Gold) or other:is(HealingOrb) then
    self:heal(0.2*self.max_hp*(self.heal_effect_m or 1))
  end
end

-- 导出模块
return CombatSystem