--[[
模块：player.lua（玩家蛇段/英雄行为）
职责：
- 玩家单位（领队 + 跟随者）的属性、移动/攻击/受击/技能与被动触发、队列管理
- 负责与物理/碰撞系统交互，并与 Arena 的敌人/投射物进行战斗
关键点：
- 生命周期：init → update → draw → 碰撞/触发/受击/死亡；followers 的增删与重算
- 队列：get_leader/get_all_units/add_follower/recalculate_followers 保持队列一致性
- 战斗：shoot/attack/dot_attack/barrage 等武器/范围/持续伤害手段与相机反馈
性能注意：
- update/draw 为热路径，避免分配临时表；粒子/特效使用 Group/Trigger 管理
依赖：
- engine.game.*（GameObject/Physics/Trigger/Group）、objects.lua（特效/道具）、enemies.lua
]]--

-- 引入配置模块
local character_skills = require('game.player.character_skills')
local CombatSystem = require('game.player.combat_system')
local SquadSystem = require('game.player.squad_system')
local BuffSystem = require('game.player.buff_system')
local PassiveSystem = require('game.player.passive_system')

Player = Object:extend()
Player:implement(GameObject)
Player:implement(Physics)
Player:implement(Unit)
-- 功能：玩家单位初始化，完成 GameObject/Unit 初始化与初始属性设置
-- 参数：args(table) - 位置/角色/等级/是否领队/被动等
-- 返回：无

function Player:init(args)
  self:init_game_object(args)
  self:init_unit()

  if self.passives then for k, v in pairs(self.passives) do self[v.passive] = v.level end end

  self.color = character_colors[self.character]
  self:set_as_rectangle(9, 9, 'dynamic', 'player')
  self.visual_shape = 'rectangle'
  self.classes = character_classes[self.character]
  self.damage_dealt = 0
  
  -- 初始化HitFX效果
  self.hfx:add('hit', 1)
  self.hfx:add('shoot', 1)
  
  -- 使用新的配置系统初始化角色技能
  local skill_config = character_skills.get_character_config(self.character)
  if skill_config and skill_config.init then
    skill_config.init(self)
    if skill_config.level_bonuses and skill_config.level_bonuses[self.level] then
      skill_config.level_bonuses[self.level](self)
    end
  end
  
  -- 原init函数后续逻辑（被动技能初始化等）
  self:init_passives_and_stats()
end

-- 被动技能系统函数委托给PassiveSystem模块
function Player:init_passives_and_stats()
  return PassiveSystem.init_passives_and_stats(self)
end

-- 功能：更新玩家单位（移动/朝向/技能计时/状态效果），并执行公共 GameObject 更新
-- 参数：dt(number) - 帧间隔时间（秒）
-- 返回：无

function Player:update(dt)
  self:update_game_object(dt)

  BuffSystem.update_all_buffs(self)

  if self.attack_sensor then self.attack_sensor:move_to(self.x, self.y) end
  if self.wide_attack_sensor then self.wide_attack_sensor:move_to(self.x, self.y) end
  if self.gun_kata_sensor then self.gun_kata_sensor:move_to(self.x, self.y) end
  self.t:set_every_multiplier('shoot', self.aspd_m)
  self.t:set_every_multiplier('attack', self.aspd_m)

  if self.leader then
    if not main.current:is(MainMenu) then
      if input.move_left.pressed and not self.move_right_pressed then self.move_left_pressed = love.timer.getTime() end
      if input.move_right.pressed and not self.move_left_pressed then self.move_right_pressed = love.timer.getTime() end
      if input.move_left.released then self.move_left_pressed = nil end
      if input.move_right.released then self.move_right_pressed = nil end

      if state.mouse_control then
        self.mouse_control_v = Vector(math.cos(self.r), math.sin(self.r)):perpendicular():dot(Vector(math.cos(self:angle_to_mouse()), math.sin(self:angle_to_mouse())))
        self.r = self.r + math.sign(self.mouse_control_v)*1.66*math.pi*dt
        table.insert(self.mouse_control_v_buffer, 1, self.mouse_control_v)
        if #self.mouse_control_v_buffer > 64 then self.mouse_control_v_buffer[65] = nil end
      else
        if input.move_left.down then self.r = self.r - 1.66*math.pi*dt end
        if input.move_right.down then self.r = self.r + 1.66*math.pi*dt end
      end
    end

    local total_v = 0
    local units = self:get_all_units()
    for _, unit in ipairs(units) do
      total_v = total_v + unit.max_v
    end
    total_v = math.floor(total_v/#units)
    self.total_v = total_v

    self:set_velocity(total_v*math.cos(self.r), total_v*math.sin(self.r))

    if not main.current.won and not main.current.choosing_passives then
      if not state.no_screen_movement then
        local vx, vy = self:get_velocity()
        local hd = math.remap(math.abs(self.x - gw/2), 0, 192, 1, 0)
        local vd = math.remap(math.abs(self.y - gh/2), 0, 108, 1, 0)
        camera.x = camera.x + math.remap(vx, -100, 100, -24*hd, 24*hd)*dt
        camera.y = camera.y + math.remap(vy, -100, 100, -8*vd, 8*vd)*dt
        if input.move_right.down then camera.r = math.lerp_angle_dt(0.01, dt, camera.r, math.pi/256)
        elseif input.move_left.down then camera.r = math.lerp_angle_dt(0.01, dt, camera.r, -math.pi/256)
          --[[
        elseif input.move_down.down then camera.r = math.lerp_angle_dt(0.01, dt, camera.r, math.pi/256)
        elseif input.move_up.down then camera.r = math.lerp_angle_dt(0.01, dt, camera.r, -math.pi/256)
        ]]--
        else camera.r = math.lerp_angle_dt(0.005, dt, camera.r, 0) end
      end
    end

    self:set_angle(self.r)

  else
    local target_distance = 10.4*(self.follower_index or 0)
    local distance_sum = 0
    local p
    local previous = self.parent
    for i, point in ipairs(self.parent.previous_positions) do
      local distance_to_previous = math.distance(previous.x, previous.y, point.x, point.y)
      distance_sum = distance_sum + distance_to_previous
      if distance_sum >= target_distance then
        p = self.parent.previous_positions[i-1]
        break
      end
      previous = point
    end

    if p then
      self:set_position(p.x, p.y)
      self.r = p.r
      if not self.following then
        spawn1:play{pitch = random:float(0.8, 1.2), volume = 0.15}
        for i = 1, random:int(3, 4) do HitParticle{group = main.current.effects, x = self.x, y = self.y, color = self.color} end
        HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 10, color = fg[0]}:scale_down(0.3):change_color(0.5, self.color)
        self.following = true
      end
    else
      self.r = self:get_angle()
    end
  end
end


-- 功能：绘制玩家单位（形状/颜色/特效），根据受击/射击效果调整缩放
-- 参数：无
-- 返回：无

function Player:draw()
  graphics.push(self.x, self.y, self.r, self.hfx.hit.x*self.hfx.shoot.x, self.hfx.hit.x*self.hfx.shoot.x)
  if self.visual_shape == 'rectangle' then
    if self.magician_invulnerable then
      graphics.rectangle(self.x, self.y, self.shape.w, self.shape.h, 3, 3, blue_transparent)
    elseif self.undead then
      graphics.rectangle(self.x, self.y, self.shape.w, self.shape.h, 3, 3, self.color, 1)
    else
      graphics.rectangle(self.x, self.y, self.shape.w, self.shape.h, 3, 3, (self.hfx.hit.f or self.hfx.shoot.f) and fg[0] or self.color)
    end

    if self.leader and state.arrow_snake then
      local x, y = self.x + 0.9*self.shape.w, self.y
      graphics.line(x + 3, y, x, y - 3, character_colors[self.character], 1)
      graphics.line(x + 3, y, x, y + 3, character_colors[self.character], 1)
    end

    if self.ouroboros_def_m and self.ouroboros_def_m > 1 then
      graphics.rectangle(self.x, self.y, 1.25*self.shape.w, 1.25*self.shape.h, 3, 3, yellow_transparent)
    end

    if self.divined then
      graphics.rectangle(self.x, self.y, 1.25*self.shape.w, 1.25*self.shape.h, 3, 3, green_transparent)
    end

    if self.fairyd then
      graphics.rectangle(self.x, self.y, 1.25*self.shape.w, 1.25*self.shape.h, 3, 3, blue_transparent)
    end
  end
  graphics.pop()
end


-- 功能：玩家与其他对象的碰撞进入处理，根据对象类型（墙体/敌人/投射物等）分发行为
-- 参数：other(GameObject) - 另一对象；contact - 碰撞接触，提供法线与接触点
-- 返回：无

function Player:on_collision_enter(other, contact)
  local x, y = contact:getPositions()

  if other:is(Wall) then
    if self.leader then
      if other.snkrx then
        main.current.level_1000_text:pull(0.2, 200, 10)
      end
      self.hfx:use('hit', 0.5, 200, 10, 0.1)
      camera:spring_shake(2, math.pi - self.r)
      self:bounce(contact:getNormal())
      local r = random:float(0.9, 1.1)
      player_hit_wall1:play{pitch = r, volume = 0.1}
      pop1:play{pitch = r, volume = 0.2}

      for i, f in ipairs(self.followers) do
        trigger:after(i*(10.6/self.v), function()
          f.hfx:use('hit', 0.5, 200, 10, 0.1)
          player_hit_wall1:play{pitch = r + 0.025*i, volume = 0.1}
          pop1:play{pitch = r + 0.05*i, volume = 0.2}
        end)
      end

      if self.wall_echo then
        if random:bool(34) then
          local target = self:get_closest_object_in_shape(Circle(self.x, self.y, 96), main.current.enemies)
          if target then
            self:barrage(self:angle_to_object(target), 2)
          else
            local r = Vector(contact:getNormal()):angle()
            self:barrage(r, 2)
          end
        end
      end

      if self.wall_rider then
        local units = self:get_all_units()
        for _, unit in ipairs(units) do unit.wall_rider_mvspd_m = 1.25 end
        trigger:after(1, function()
          for _, unit in ipairs(units) do unit.wall_rider_mvspd_m = 1 end
        end, 'wall_rider')
      end
    end

  elseif table.any(main.current.enemies, function(v) return other:is(v) end) then
    other:push(random:float(25, 35)*(self.knockback_m or 1), self:angle_to_object(other))
    if self.character == 'vagrant' or self.character == 'psykeeper' then other:hit(2*self.dmg)
    else other:hit(self.dmg) end
    if other.headbutting then
      self:hit((4 + math.floor(other.level/3))*other.dmg)
      other.headbutting = false
    else self:hit(other.dmg) end
    HitCircle{group = main.current.effects, x = x, y = y, rs = 6, color = fg[0], duration = 0.1}
    for i = 1, 2 do HitParticle{group = main.current.effects, x = x, y = y, color = self.color} end
    for i = 1, 2 do HitParticle{group = main.current.effects, x = x, y = y, color = other.color} end
  end
end


-- 功能：玩家受击处理，应用无敌/亡灵判定、播放反馈、扣减生命并触发死亡/存活分支
-- 参数：damage(number) - 伤害值；from_undead(boolean) - 是否来自亡灵（绕过亡灵免疫）
-- 返回：无

-- 战斗系统函数委托给CombatSystem模块
function Player:hit(damage, from_undead)
  return CombatSystem.hit(self, damage, from_undead)
end



function Player:sorcerer_repeat()
  local enemies = self.group:get_objects_by_classes(main.current.enemies)
  if not enemies then return end
  local enemy = random:table(enemies)
  if enemy then
    if self.gravity_field then
      ForceArea{group = main.current.effects, x = enemy.x, y = enemy.y, rs = self.area_size_m*24, color = fg[0], character = 'gravity_field', parent = self}
    end
  end

  local enemy = random:table(enemies)
  if enemy then
    if self.burning_field then
      fire1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
      DotArea{group = main.current.effects, x = enemy.x, y = enemy.y, rs = self.area_size_m*24, color = red[0], dmg = 30*self.area_dmg_m*(self.dot_dmg_m or 1), duration = 2, character = 'burning_field'}
    end
  end

  local enemy = random:table(enemies)
  if enemy then
    if self.freezing_field then
      frost1:play{pitch = random:float(0.8, 1.2), volume = 0.3}
      elementor1:play{pitch = random:float(0.9, 1.1), volume = 0.3}
      Area{group = main.current.effects, x = enemy.x, y = enemy.y, w = self.area_size_m*36, color = blue[0], character = 'freezing_field', parent = self}
    end
  end
end


-- 功能：治疗玩家，播放反馈并限制生命不超过最大值
-- 参数：amount(number) - 回复生命值
-- 返回：无

function Player:heal(amount)
  return CombatSystem.heal(self, amount)
end



function Player:chain_infuse(duration)
  self.chain_infused = true
  self.t:after(duration or 2, function() self.chain_infused = false end, 'chain_infuse')
-- 功能：获取全部单位（领队 + 跟随者）；保证 leader 在索引 1
-- 参数：无
-- 返回：table - 按顺序的单位数组

end


-- 功能：返回当前编队的领队对象
-- 参数：无
-- 返回：Player - 领队

-- 队伍管理系统函数委托给SquadSystem模块
function Player:get_all_units()
  return SquadSystem.get_all_units(self)
end


-- 功能：按角色标识从编队中获取对应单位
-- 参数：character(string) - 角色名
-- 返回：Player|nil - 命中则返回单位

function Player:get_leader()
  return SquadSystem.get_leader(self)
end


function Player:get_unit(character)
  return SquadSystem.get_unit(self, character)
end


-- 功能：当领队死亡或成员阵亡时，重算队列；必要时选出新领队并重建跟随关系
-- 参数：无
-- 返回：无

function Player:recalculate_followers()
  return SquadSystem.recalculate_followers(self)
end


function Player:add_follower(unit)
  return SquadSystem.add_follower(self, unit)
end


-- 功能：射击/发射投射物（含多职业分支、暴击与特效），并可能触发二次连发/召唤
-- 参数：r(number) - 发射角度；mods(table|nil) - 附加修饰（穿透、弹射、追踪等）
-- 返回：无
function Player:shoot(r, mods)
  return CombatSystem.shoot(self, r, mods)
end


-- 功能：近战/范围攻击，在指定位置生成 Area 造成一次性范围伤害并播放对应音效
-- 参数：area(number|nil) - 区域大小基准；mods(table|nil) - 位置/颜色等修饰
-- 返回：无
function Player:attack(area, mods)
  return CombatSystem.attack(self, area, mods)
end


-- 功能：持续伤害区域（DoT），在区域内周期性对敌方造成伤害
-- 参数：area(number|nil) - 半径基准；mods(table|nil) - 位置/颜色等修饰
-- 返回：无
function Player:dot_attack(area, mods)
  mods = mods or {}
  camera:shake(2, 0.5)
  self.hfx:use('shoot', 0.25)
  local t = {group = main.current.effects, x = mods.x or self.x, y = mods.y or self.y, r = self.r, rs = self.area_size_m*(area or 64), color = self.color, dmg = self.area_dmg_m*self.dmg*(self.dot_dmg_m or 1),
    character = self.character, level = self.level, parent = self}
  DotArea(table.merge(t, mods))

  dot1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
end

-- 功能：弹幕/多连射，延迟队列发出多枚投射物并支持穿透/弹射/追踪
-- 参数：
--   r(number) - 基准角度
--   n(number|nil) - 发射数量，默认 8
--   pierce(number|nil) - 穿透层数
--   ricochet(number|nil) - 弹射次数
--   shoot_5(boolean|nil) - 五连特例
--   homing(boolean|nil) - 是否追踪
-- 返回：无
function Player:barrage(r, n, pierce, ricochet, shoot_5, homing)
  return CombatSystem.barrage(self, r, n, pierce, ricochet, shoot_5, homing)
end