--[[
模块：passive_system.lua
职责：被动技能系统，包括所有被动技能的初始化和效果处理
目的：将被动技能逻辑从player.lua分离，提高代码组织性和可维护性
]]--

local constants = require('game.player.player_constants')

-- 被动技能系统模块
local PassiveSystem = {}

-- 功能：初始化被动技能和状态
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.init_passives_and_stats(self)
  self:calculate_stats(true)

  -- 领队初始化：位置追踪和跟随者管理
  if self.leader then
    self.previous_positions = {}
    self.followers = {}
    self.t:every(0.01, function()
      table.insert(self.previous_positions, 1, {x = self.x, y = self.y, r = self.r})
      if #self.previous_positions > 256 then 
        self.previous_positions[257] = nil 
      end
    end)
  end

  -- 衔尾蛇技术 - 自动攻击系统
  if self.ouroboros_technique_r then
    self.t:after(0.01, function()
      local interval = (self.ouroboros_technique_r == 1 and 0.5) or 
                       (self.ouroboros_technique_r == 2 and 0.33) or 
                       (self.ouroboros_technique_r == 3 and 0.25)
      self.t:every(interval, function()
        if self.leader and ((state.mouse_control and table.all(self.mouse_control_v_buffer, function(v) return v >= 0.5 end)) or 
           (self.move_right_pressed and love.timer.getTime() - self.move_right_pressed > 1)) then
          local target = self:get_closest_object_in_shape(Circle(self.x, self.y, 96), main.current.enemies)
          if target then
            local units = self:get_all_units()
            local unit = random:table(units)
            unit:barrage(unit:angle_to_object(target), 1)
          else
            local units = self:get_all_units()
            local cx, cy = 0, 0
            for _, unit in ipairs(units) do
              cx = cx + unit.x
              cy = cy + unit.y
            end
            cx = cx/#units
            cy = cy/#units
            local unit = random:table(units)
            unit:barrage(unit:angle_from_point(cx, cy), 1)
          end
        end
      end)
    end)
  end

  -- 基础被动技能初始化
  PassiveSystem.init_basic_passives(self)
  
  -- 职业特定被动技能
  PassiveSystem.init_class_specific_passives(self)
  
  -- 领队专属被动技能
  if self.leader then
    PassiveSystem.init_leader_passives(self)
  end
  
  -- 特殊角色初始化
  PassiveSystem.init_special_character_effects(self)
  
  -- 鼠标控制缓冲区初始化
  self.mouse_control_v_buffer = {}

  -- 主菜单特殊处理
  if main.current and main.current:is(MainMenu) then
    self.r = random:table{-math.pi/4, math.pi/4, 3*math.pi/4, -3*math.pi/4}
    self:set_angle(self.r)
  end
end

-- 功能：初始化基础被动技能
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.init_basic_passives(self)
  -- 蜈蚣 - 移动速度提升
  if self.centipede then 
    self.centipede_mvspd_m = (self.centipede == 1 and 1.1) or 
                             (self.centipede == 2 and 1.2) or 
                             (self.centipede == 3 and 1.3) 
  end
  
  -- 放大 - 区域伤害提升
  if self.amplify then 
    self.amplify_area_dmg_m = (self.amplify == 1 and 1.2) or 
                              (self.amplify == 2 and 1.35) or 
                              (self.amplify == 3 and 1.5) 
  end

  -- 弩炮 - 投射物伤害提升
  if self.ballista and launches_projectiles(self.character) then
    self.ballista_dmg_m = (self.ballista == 1 and 1.2) or 
                          (self.ballista == 2 and 1.35) or 
                          (self.ballista == 3 and 1.5)
  end

  -- 时间术 - 法师攻速提升
  if self.chronomancy then
    if table.any(self.classes, function(v) return v == 'mage' end) then
      self.chronomancy_aspd_m = (self.chronomancy == 1 and 1.15) or 
                                (self.chronomancy == 2 and 1.25) or 
                                (self.chronomancy == 3 and 1.35)
    end
  end

  -- 放大镜 - 区域大小提升
  if self.magnify then
    self.magnify_area_size_m = (self.magnify == 1 and 1.2) or 
                               (self.magnify == 2 and 1.35) or 
                               (self.magnify == 3 and 1.5)
  end

  -- 反击 - 受伤害加成
  if self.payback then
    self.payback_dmg_m = 1
  end

  -- 咒术大师 - 诅咒持续时间延长
  if self.hex_master then
    self.hex_duration_m = 1.25
  end

  -- 不屈姿态 - 防御力初始化
  if self.unrelenting_stance then
    self.unrelenting_stance_def_m = 1
  end
end

-- 功能：初始化职业特定被动技能
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.init_class_specific_passives(self)
  -- 觉醒 - 法师增强
  if self.leader and self.awakening then
    main.current.t:after(0.1, function()
      local units = self:get_all_units()
      local mages = {}
      for _, unit in ipairs(units) do
        if table.any(unit.classes, function(v) return v == 'mage' end) then
          table.insert(mages, unit)
        end
      end
      local mage = random:table(mages)
      if mage then
        local runs = 0
        while table.any(non_attacking_characters, function(v) return v == mage.character end) and runs < 1000 do 
          mage = random:table(mages)
          runs = runs + 1 
        end
        mage.awakening_aspd_m = (self.awakening == 1 and 1.5) or 
                                (self.awakening == 2 and 1.75) or 
                                (self.awakening == 3 and 2)
        mage.awakening_dmg_m = (self.awakening == 1 and 1.5) or 
                               (self.awakening == 2 and 1.75) or 
                               (self.awakening == 3 and 2)
      end
    end)
  end

  -- 神罚 - 法师雷电攻击
  if self.leader and self.divine_punishment then
    main.current.t:every(5, function()
      local units = self:get_all_units()
      local mages = {}
      for _, unit in ipairs(units) do
        if table.any(unit.classes, function(v) return v == 'mage' end) then
          table.insert(mages, unit)
        end
      end
      local enemies = main.current.main:get_objects_by_classes(main.current.enemies)
      if #enemies > 0 then
        thunder1:play{volume = 0.3}
        camera:shake(4, 0.5)
      end
      for _, enemy in ipairs(enemies) do
        enemy:hit(10*#mages)
        LightningLine{group = main.current.effects, src = {x = enemy.x, y = enemy.y - 32}, 
                      dst = enemy, color = blue[0], duration = 0.2}
        _G[random:table{'spark1', 'spark2', 'spark3'}]:play{pitch = random:float(0.9, 1.1), volume = 0.3}
      end
    end, nil, nil, 'divine_punishment')
  end

  -- 不屈立场 - 战士防御提升
  if self.unwavering_stance and table.any(self.classes, function(v) return v == 'warrior' end) then
    self.unwavering_stance_def_m = 1
    self.t:every(5, function()
      self.unwavering_stance_def_m = self.unwavering_stance_def_m + 
        ((self.unwavering_stance == 1 and 0.04) or 
         (self.unwavering_stance == 2 and 0.08) or 
         (self.unwavering_stance == 3 and 0.12))
    end)
  end

  -- 释放 - 毁灭者持续增强
  if self.unleash and table.any(self.classes, function(v) return v == 'nuker' end) then
    self.unleash_area_dmg_m = 1
    self.unleash_area_size_m = 1
    self.t:every(1, function()
      self.unleash_area_dmg_m = self.unleash_area_dmg_m + 0.01
      self.unleash_area_size_m = self.unleash_area_size_m + 0.01
      if self.dot_area then
        self.dot_area:scale(self.unleash_area_size_m)
      end
    end)
  end

  -- 强化 - 附魔师增强
  if self.reinforce then
    main.current.t:after(0.1, function()
      local units = self:get_all_units()
      local any_enchanter = false
      for _, unit in ipairs(units) do
        if table.any(unit.classes, function(v) return v == 'enchanter' end) then
          any_enchanter = true
          break
        end
      end
      if any_enchanter then
        local v = (self.reinforce == 1 and 1.1) or 
                  (self.reinforce == 2 and 1.2) or 
                  (self.reinforce == 3 and 1.3) or 1
        self.reinforce_dmg_m = v
        self.reinforce_def_m = v
        self.reinforce_aspd_m = v
      end
    end)
  end

  -- 魔法附魔 - 附魔师效果
  if self.enchanted then
    main.current.t:after(0.1, function()
      local units = self:get_all_units()
      local enchanter_amount = 0
      for _, unit in ipairs(units) do
        if table.any(unit.classes, function(v) return v == 'enchanter' end) then
          enchanter_amount = enchanter_amount + 1
        end
      end

      if enchanter_amount >= 2 then
        local unit = random:table(units)
        local runs = 0
        if unit then
          while table.any(non_attacking_characters, function(v) return v == unit.character end) and runs < 1000 do 
            unit = random:table(units)
            runs = runs + 1 
          end
          unit.enchanted_aspd_m = (self.enchanted == 1 and 1.33) or 
                                  (self.enchanted == 2 and 1.66) or 
                                  (self.enchanted == 3 and 1.99)
        end
      end
    end)
  end
end

-- 功能：初始化领队专属被动技能
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.init_leader_passives(self)
  -- 献祭 - 自残增伤系统
  if self.immolation then
    main.current.t:after(0.1, function()
      local units = self:get_all_units()
      local unit_1 = random:table_remove(units)
      local unit_2 = random:table_remove(units)
      local unit_3 = random:table_remove(units)
      
      -- 给3个单位施加持续伤害
      if unit_1 then 
        unit_1.t:every(2, function() unit_1:hit(0.05*unit_1.max_hp) end) 
      end
      if unit_2 then 
        unit_2.t:every(2, function() unit_2:hit(0.05*unit_2.max_hp) end) 
      end
      if unit_3 then 
        unit_3.t:every(2, function() unit_3:hit(0.05*unit_3.max_hp) end) 
      end
      
      -- 给所有单位增加伤害提升
      local units = self:get_all_units()
      for _, unit in ipairs(units) do
        unit.immolation_dmg_m = 1
        unit.t:every(2, function() 
          unit.immolation_dmg_m = unit.immolation_dmg_m + 0.08 
        end)
      end
    end)
  end

  -- 第5位攻击
  if self.shoot_5 then
    main.current.t:after(0.1, function()
      main.current.t:every(0.33, function()
        local units = main.current.player:get_all_units()
        local unit = units[5]
        if unit then
          local target = unit:get_closest_object_in_shape(Circle(unit.x, unit.y, 96), main.current.enemies)
          if target then
            unit:barrage(unit:angle_to_object(target), 1, nil, nil, true)
          else
            unit:barrage(random:float(0, 2*math.pi), 1, nil, nil, true)
          end
        end
      end)
    end)
  end

  -- 第6位自残
  if self.death_6 then
    main.current.t:after(0.1, function()
      main.current.t:every(3, function()
        flagellant1:play{pitch = random:float(0.95, 1.05), volume = 0.4}
        local units = main.current.player:get_all_units()
        local unit = units[6]
        if unit then
          hit2:play{pitch = random:float(0.95, 1.05), volume = 0.4}
          unit:hit(0.1*unit.max_hp)
        end
      end)
    end)
  end

  -- 心理能力者投射物
  PassiveSystem.init_psyker_effects(self)

  -- 心理泄漏
  if self.psycholeak then
    main.current.t:every(10, function()
      local unit = main.current.player
      Projectile{group = main.current.main, x = unit.x + 24*math.cos(unit.r), 
                 y = unit.y + 24*math.sin(unit.r), color = fg[0], v = 200, 
                 dmg = unit.dmg, character = 'psyker', parent = unit}
    end)
  end

  -- 神圣祝福 - 治疗球生成
  if self.divine_blessing then
    main.current.t:every(8, function()
      local x, y = random:float(main.current.x1 + 16, main.current.x2 - 16), 
                   random:float(main.current.y1 + 16, main.current.y2 - 16)
      SpawnEffect{group = main.current.effects, x = x, y = y, color = green[0], 
                  action = function(x, y)
                    local check_circle = Circle(x, y, 2)
                    local objects = main.current.main:get_objects_in_shape(check_circle, 
                      {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Bomb, Pet, Turret, Sentry, Automaton})
                    if #objects == 0 then 
                      HealingOrb{group = main.current.main, x = x, y = y} 
                    end
                  end}
    end)
  end
end

-- 功能：初始化心理能力者效果
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.init_psyker_effects(self)
  self.t:after(1, function()
    local units = self:get_all_units()
    
    -- 为所有心理能力者生成基础投射物
    for _, unit in ipairs(units) do
      if table.any(unit.classes, function(v) return v == 'psyker' end) then
        Projectile{group = main.current.main, x = unit.x + 24*math.cos(unit.r), 
                   y = unit.y + 24*math.sin(unit.r), color = fg[0], v = 200, 
                   dmg = unit.dmg, character = 'psyker', parent = unit}
      end
    end

    -- 收集所有心理能力者
    local psykers = {}
    for _, unit in ipairs(units) do
      if table.any(unit.classes, function(v) return v == 'psyker' end) then
        table.insert(psykers, unit)
      end
    end

    -- 根据心理能力者等级生成额外投射物
    local extra_orbs = (main.current.psyker_level == 2 and 4) or 
                       (main.current.psyker_level == 1 and 2) or 0
    for i = 1, extra_orbs do
      local unit = random:table(#psykers > 0 and psykers or units)
      Projectile{group = main.current.main, x = unit.x + 24*math.cos(unit.r), 
                 y = unit.y + 24*math.sin(unit.r), color = fg[0], v = 200, 
                 dmg = unit.dmg, character = 'psyker', parent = unit}
    end

    -- 心理能力者球体增强
    if self.psyker_orbs then
      local orb_count = (self.psyker_orbs == 1 and 1) or 
                        (self.psyker_orbs == 2 and 2) or 
                        (self.psyker_orbs == 3 and 4) or 0
      for i = 1, orb_count do
        local unit = random:table(#psykers > 0 and psykers or units)
        Projectile{group = main.current.main, x = unit.x + 24*math.cos(unit.r), 
                   y = unit.y + 24*math.sin(unit.r), color = fg[0], v = 200, 
                   dmg = unit.dmg, character = 'psyker', parent = unit}
      end
    end
  end)
end

-- 功能：初始化特殊角色效果
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.init_special_character_effects(self)
  -- 苦行者3级特殊血量
  if self.character == 'flagellant' and self.level == 3 then
    self.hp = 2*self.max_hp
  end
end

-- 功能：应用被动技能效果到属性
-- 参数：self - Player实例
-- 返回：无
function PassiveSystem.apply_passive_multipliers(self)
  -- 这个函数会在calculate_stats中被调用
  -- 应用各种被动技能的倍率修正
  
  -- 移动速度修正
  if self.centipede_mvspd_m then
    self.mvspd = self.mvspd * self.centipede_mvspd_m
  end
  
  -- 攻击速度修正
  local aspd_multipliers = {
    self.chronomancy_aspd_m or 1,
    self.awakening_aspd_m or 1,
    self.enchanted_aspd_m or 1,
    self.reinforce_aspd_m or 1,
    self.fairy_aspd_m or 1
  }
  
  for _, multiplier in ipairs(aspd_multipliers) do
    self.aspd_m = (self.aspd_m or 1) * multiplier
  end
  
  -- 伤害修正
  local dmg_multipliers = {
    self.ballista_dmg_m or 1,
    self.awakening_dmg_m or 1,
    self.reinforce_dmg_m or 1,
    self.immolation_dmg_m or 1,
    self.payback_dmg_m or 1,
    self.flagellant_dmg_m or 1
  }
  
  for _, multiplier in ipairs(dmg_multipliers) do
    self.dmg = self.dmg * multiplier
  end
  
  -- 区域伤害修正
  if self.amplify_area_dmg_m then
    self.area_dmg_m = (self.area_dmg_m or 1) * self.amplify_area_dmg_m
  end
  if self.unleash_area_dmg_m then
    self.area_dmg_m = (self.area_dmg_m or 1) * self.unleash_area_dmg_m
  end
  
  -- 区域大小修正
  if self.magnify_area_size_m then
    self.area_size_m = (self.area_size_m or 1) * self.magnify_area_size_m
  end
  if self.unleash_area_size_m then
    self.area_size_m = (self.area_size_m or 1) * self.unleash_area_size_m
  end
  
  -- 防御力修正
  local def_multipliers = {
    self.unwavering_stance_def_m or 1,
    self.unrelenting_stance_def_m or 1,
    self.reinforce_def_m or 1
  }
  
  for _, multiplier in ipairs(def_multipliers) do
    self.def = (self.def or 0) * multiplier
  end
end

-- 导出模块
return PassiveSystem