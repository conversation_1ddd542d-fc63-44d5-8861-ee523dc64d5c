--[[
模块：buff_system.lua
职责：增益效果系统，统一管理角色、职业、被动技能和位置相关的所有buff效果
目的：将buff计算逻辑从player.lua分离，提高代码组织性和可维护性
]]--

local BuffSystem = {}

-- 角色特定增益效果
local CharacterBuffs = {}

-- 功能：处理特定角色的专属增益效果
-- 参数：self - Player实例
-- 返回：无
function CharacterBuffs.update_character_buffs(self)
  -- Squire - 为所有队伍成员提供伤害和防御加成
  if self.character == 'squire' then
    local all_units = self:get_all_units()
    for _, unit in ipairs(all_units) do
      unit.squire_dmg_m = 1.2
      unit.squire_def_m = 1.2
      if self.level == 3 then
        unit.squire_dmg_m = 1.5
        unit.squire_def_m = 1.5
        unit.squire_aspd_m = 1.3
        unit.squire_mvspd_m = 1.3
      end
    end
  end
  
  -- Chronomancer - 为所有队伍成员提供攻击速度加成
  if self.character == 'chronomancer' then
    local all_units = self:get_all_units()
    for _, unit in ipairs(all_units) do
      unit.chronomancer_aspd_m = 1.2
    end
  end
  
  -- Vagrant - 基于激活职业数量的伤害和攻击速度加成
  if self.character == 'vagrant' and self.level == 3 then
    local class_levels = get_class_levels(self:get_all_units())
    local number_of_active_sets = 0
    if class_levels.ranger >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.warrior >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.mage >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.rogue >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.healer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.conjurer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.enchanter >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.psyker >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.nuker >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.curser >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.forcer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.swarmer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.voider >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.sorcerer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.mercenary >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.explorer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    self.vagrant_dmg_m = 1 + 0.1*number_of_active_sets
    self.vagrant_aspd_m = 1 + 0.1*number_of_active_sets
  end
  
  -- Swordsman - 3级时双倍伤害
  if self.character == 'swordsman' and self.level == 3 then
    self.swordsman_dmg_m = 2
  end
  
  -- Outlaw - 3级时1.5倍攻击速度
  if self.character == 'outlaw' and self.level == 3 then
    self.outlaw_aspd_m = 1.5
  end
  
  -- Flagellant - 3级时双倍生命值
  if self.character == 'flagellant' and self.level == 3 then
    self.flagellant_hp_m = 2
  end
end

-- 职业系统增益效果
local ClassBuffs = {}

-- 功能：处理基于职业等级的全局增益效果
-- 参数：self - Player实例
-- 返回：无
function ClassBuffs.update_class_buffs(self)
  -- Ranger - 连发概率
  if table.any(self.classes, function(v) return v == 'ranger' end) then
    if main.current.ranger_level == 2 then self.chance_to_barrage = 16
    elseif main.current.ranger_level == 1 then self.chance_to_barrage = 8
    elseif main.current.ranger_level == 0 then self.chance_to_barrage = 0 end
  end

  -- Warrior - 防御力加成
  if table.any(self.classes, function(v) return v == 'warrior' end) then
    if main.current.warrior_level == 2 then self.warrior_def_a = 50
    elseif main.current.warrior_level == 1 then self.warrior_def_a = 25
    elseif main.current.warrior_level == 0 then self.warrior_def_a = 0 end
  end

  -- Nuker - 区域大小和伤害加成
  if table.any(self.classes, function(v) return v == 'nuker' end) then
    if main.current.nuker_level == 2 then self.nuker_area_size_m = 1.25; self.nuker_area_dmg_m = 1.25
    elseif main.current.nuker_level == 1 then self.nuker_area_size_m = 1.15; self.nuker_area_dmg_m = 1.15
    elseif main.current.nuker_level == 0 then self.nuker_area_size_m = 1; self.nuker_area_dmg_m = 1 end
  end

  -- Conjurer - 召唤物增益
  if main.current.conjurer_level == 2 then self.conjurer_buff_m = 1.5
  elseif main.current.conjurer_level == 1 then self.conjurer_buff_m = 1.25
  else self.conjurer_buff_m = 1 end

  -- Rogue - 暴击概率
  if table.any(self.classes, function(v) return v == 'rogue' end) then
    if main.current.rogue_level == 2 then self.chance_to_crit = 30
    elseif main.current.rogue_level == 1 then self.chance_to_crit = 15
    elseif main.current.rogue_level == 0 then self.chance_to_crit = 0 end
  end

  -- Enchanter - 伤害倍率
  if main.current.enchanter_level == 2 then self.enchanter_dmg_m = 1.25
  elseif main.current.enchanter_level == 1 then self.enchanter_dmg_m = 1.15
  else self.enchanter_dmg_m = 1 end

  -- Explorer - 基于激活职业数量的加成
  if table.any(self.classes, function(v) return v == 'explorer' end) then
    local class_levels = get_class_levels(self:get_all_units())
    local number_of_active_sets = 0
    if class_levels.ranger >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.warrior >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.mage >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.rogue >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.healer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.conjurer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.enchanter >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.psyker >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.nuker >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.curser >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.forcer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.swarmer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.voider >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.sorcerer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.mercenary >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    if class_levels.explorer >= 1 then number_of_active_sets = number_of_active_sets + 1 end
    self.explorer_dmg_m = 1 + 0.15*number_of_active_sets
    self.explorer_aspd_m = 1 + 0.15*number_of_active_sets
  end

  -- Forcer - 击退倍率
  if main.current.forcer_level == 2 then self.knockback_m = 1.5
  elseif main.current.forcer_level == 1 then self.knockback_m = 1.25
  else self.knockback_m = 1 end

  -- Voider - 持续伤害倍率
  if table.any(self.classes, function(v) return v == 'voider' end) then
    if main.current.voider_level == 2 then self.dot_dmg_m = 1.4
    elseif main.current.voider_level == 1 then self.dot_dmg_m = 1.2
    else self.dot_dmg_m = 1 end
  end
end

-- 被动技能增益效果
local PassiveBuffs = {}

-- 功能：处理被动技能相关的增益效果
-- 参数：self - Player实例
-- 返回：无
function PassiveBuffs.update_passive_buffs(self)
  -- Blessing - 治疗效果加成
  self.heal_effect_m = 1
  if self.blessing then 
    self.heal_effect_m = self.heal_effect_m*((self.blessing == 1 and 1.1) or (self.blessing == 2 and 1.2) or (self.blessing == 3 and 1.3)) 
  end

  -- Force Push - 击退加成
  if self.force_push then 
    self.knockback_m = (self.knockback_m or 1)*1.25 
  end

  -- Call of the Void - 持续伤害加成
  if self.call_of_the_void then 
    self.dot_dmg_m = (self.dot_dmg_m or 1)*((self.call_of_the_void == 1 and 1.3) or (self.call_of_the_void == 2 and 1.6) or (self.call_of_the_void == 3 and 1.9) or 1) 
  end

  -- Ouroboros Technique - 基于移动状态的防御加成
  if self.ouroboros_technique_l and self.leader then
    local units = self:get_all_units()
    if (state.mouse_control and table.all(self.mouse_control_v_buffer, function(v) return v <= -0.5 end)) or 
       (self.move_left_pressed and love.timer.getTime() - self.move_left_pressed > 1) then
      for _, unit in ipairs(units) do
        unit.ouroboros_def_m = (self.ouroboros_technique_l == 1 and 1.15) or (self.ouroboros_technique_l == 2 and 1.25) or (self.ouroboros_technique_l == 3 and 1.35)
      end
    else
      for _, unit in ipairs(units) do
        unit.ouroboros_def_m = 1
      end
    end
  end

  -- Berserking - 基于生命值百分比的攻击速度加成
  if self.berserking and table.any(self.classes, function(v) return v == 'warrior' end) then
    self.berserking_aspd_m = math.remap(self.hp/self.max_hp, 0, 1, (self.berserking == 1 and 1.5) or (self.berserking == 2 and 1.75) or (self.berserking == 3 and 2), 1)
  end

  -- Haste - 基于时间的移动速度加成
  if self.haste then
    if self.hasted then
      self.haste_mvspd_m = math.clamp(math.remap(love.timer.getTime() - self.hasted, 0, 4, 1.5, 1), 1, 1.5)
    else 
      self.haste_mvspd_m = 1 
    end
  end
end

-- 位置相关增益效果
local PositionalBuffs = {}

-- 功能：处理基于队伍位置或特殊条件的增益效果
-- 参数：self - Player实例
-- 返回：无
function PositionalBuffs.update_positional_buffs(self)
  -- Speed 3 - 第2位置的攻击速度加成
  if self.speed_3 and self.follower_index == 2 then
    self.speed_3_aspd_m = 1.5
  end

  -- Damage 4 - 第3位置的伤害加成
  if self.damage_4 and self.follower_index == 3 then
    self.damage_4_dmg_m = 1.3
  end

  -- Defensive Stance - 领队或末尾位置的防御加成
  if self.defensive_stance and self.leader then
    self.defensive_stance_def_m = (self.defensive_stance == 1 and 1.1) or (self.defensive_stance == 2 and 1.2) or (self.defensive_stance == 3 and 1.3)
  end

  if self.defensive_stance and not self.leader and self.follower_index == #self.parent.followers then
    self.defensive_stance_def_m = (self.defensive_stance == 1 and 1.1) or (self.defensive_stance == 2 and 1.2) or (self.defensive_stance == 3 and 1.3)
  end

  -- Offensive Stance - 领队或末尾位置的伤害加成
  if self.offensive_stance and self.leader then
    self.offensive_stance_dmg_m = (self.offensive_stance == 1 and 1.1) or (self.offensive_stance == 2 and 1.2) or (self.offensive_stance == 3 and 1.3)
  end

  if self.offensive_stance and not self.leader and self.follower_index == #self.parent.followers then
    self.offensive_stance_dmg_m = (self.offensive_stance == 1 and 1.1) or (self.offensive_stance == 2 and 1.2) or (self.offensive_stance == 3 and 1.3)
  end

  -- Last Stand - 孤身作战时的全面加成
  if self.leader and #self.followers == 0 and self.last_stand then
    self.last_stand_dmg_m = 1.2
    self.last_stand_def_m = 1.2
    self.last_stand_aspd_m = 1.2
    self.last_stand_area_size_m = 1.2
    self.last_stand_area_dmg_m = 1.2
    self.last_stand_mvspd_m = 1.2
  end

  -- Dividends - 基于金币数量的伤害加成
  if self.dividends and table.any(self.classes, function(v) return v == 'mercenary' end) then
    self.dividends_dmg_m = (1 + gold/100)
  end
end

-- 增益计算器
local BuffCalculator = {}

-- 功能：将所有单项增益合并为最终的综合增益数值
-- 参数：self - Player实例
-- 返回：无
function BuffCalculator.calculate_final_buffs(self)
  -- 合并所有增益效果为最终数值
  self.buff_def_a = (self.warrior_def_a or 0)
  self.buff_aspd_m = (self.chronomancer_aspd_m or 1)*(self.vagrant_aspd_m or 1)*(self.outlaw_aspd_m or 1)*(self.fairy_aspd_m or 1)*(self.psyker_aspd_m or 1)*(self.chronomancy_aspd_m or 1)*(self.awakening_aspd_m or 1)*(self.berserking_aspd_m or 1)*(self.reinforce_aspd_m or 1)*(self.squire_aspd_m or 1)*(self.speed_3_aspd_m or 1)*(self.last_stand_aspd_m or 1)*(self.enchanted_aspd_m or 1)*(self.explorer_aspd_m or 1)*(self.magician_aspd_m or 1)
  self.buff_dmg_m = (self.squire_dmg_m or 1)*(self.vagrant_dmg_m or 1)*(self.enchanter_dmg_m or 1)*(self.swordsman_dmg_m or 1)*(self.flagellant_dmg_m or 1)*(self.psyker_dmg_m or 1)*(self.ballista_dmg_m or 1)*(self.awakening_dmg_m or 1)*(self.reinforce_dmg_m or 1)*(self.payback_dmg_m or 1)*(self.immolation_dmg_m or 1)*(self.damage_4_dmg_m or 1)*(self.offensive_stance_dmg_m or 1)*(self.last_stand_dmg_m or 1)*(self.dividends_dmg_m or 1)*(self.explorer_dmg_m or 1)
  self.buff_def_m = (self.squire_def_m or 1)*(self.ouroboros_def_m or 1)*(self.unwavering_stance_def_m or 1)*(self.reinforce_def_m or 1)*(self.defensive_stance_def_m or 1)*(self.last_stand_def_m or 1)*(self.unrelenting_stance_def_m or 1)*(self.hardening_def_m or 1)
  self.buff_area_size_m = (self.nuker_area_size_m or 1)*(self.magnify_area_size_m or 1)*(self.unleash_area_size_m or 1)*(self.last_stand_area_size_m or 1)
  self.buff_area_dmg_m = (self.nuker_area_dmg_m or 1)*(self.amplify_area_dmg_m or 1)*(self.unleash_area_dmg_m or 1)*(self.last_stand_area_dmg_m or 1)
  self.buff_mvspd_m = (self.wall_rider_mvspd_m or 1)*(self.centipede_mvspd_m or 1)*(self.squire_mvspd_m or 1)*(self.last_stand_mvspd_m or 1)*(self.haste_mvspd_m or 1)
  self.buff_hp_m = (self.flagellant_hp_m or 1)
  
  -- 重新计算所有属性
  self:calculate_stats()
end

-- 主要接口函数
-- 功能：更新所有类型的增益效果
-- 参数：self - Player实例
-- 返回：无
function BuffSystem.update_all_buffs(self)
  -- 按顺序更新各类增益效果
  CharacterBuffs.update_character_buffs(self)
  ClassBuffs.update_class_buffs(self)
  PassiveBuffs.update_passive_buffs(self)
  PositionalBuffs.update_positional_buffs(self)
  BuffCalculator.calculate_final_buffs(self)
end

-- 导出模块
return BuffSystem