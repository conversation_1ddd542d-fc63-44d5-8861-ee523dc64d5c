--[[
模块：player/init.lua（玩家模块聚合入口）
职责：
- 统一加载player模块的所有子模块
- 管理模块间的加载依赖顺序
- 作为player模块的统一入口点
关键点：
- 按照依赖关系正确排序require语句
- 常量和配置模块需要最先加载
- items和effects模块没有相互依赖，较早加载
- areas模块被projectiles和其他模块引用，需要较早加载
- projectiles被summons等模块引用，需要在summons之前加载
- player类引用了所有其他类，最后加载
]]--

-- 常量定义（基础配置，被所有模块引用）
require 'game.player.player_constants'

-- 角色技能配置（被player类引用）
require 'game.player.character_skills'

-- 战斗系统（被player类引用）
require 'game.player.combat_system'

-- 队伍管理系统（被player类引用）
require 'game.player.squad_system'

-- 被动技能系统（被player类引用）
require 'game.player.passive_system'

-- 增益效果系统（被player类引用）
require 'game.player.buff_system'

-- 区域效果类（基础类，被很多模块引用）
require 'game.player.areas'

-- 投射物类（引用区域效果类，被其他模块引用）  
require 'game.player.projectiles'

-- 基础物品类（引用投射物）
require 'game.player.items'

-- 特殊效果类（引用区域效果）
require 'game.player.effects'

-- 召唤物类（引用投射物和区域效果）
require 'game.player.summons'

-- 玩家类（引用所有上述类）
require 'game.player.player'