--[[
模块：character_skills.lua
职责：所有角色的技能配置定义，使用配置驱动替代硬编码
目的：将1000+行的if-elseif逻辑转为可维护的配置表
]]--

local constants = require('game.player.player_constants')
local AR = constants.ATTACK_RANGE
local CD = constants.COOLDOWN
local PS = constants.PROJECTILE_SPEED
local AS = constants.AREA_SIZE
local CC = constants.CHAIN_COUNT
local PC = constants.PIERCE_COUNT

-- 角色技能配置表
local CHARACTER_SKILLS = {
  -- RANGER 类角色
  vagrant = {
    attack_range = AR.MEDIUM,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:cooldown(CD.FAST, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end, 
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy))
          end
        end, nil, nil, 'shoot')
    end,
    level_bonuses = {
      [3] = function(self) 
        -- 3级vagrant根据激活职业数获得加成
      end
    }
  },

  archer = {
    attack_range = AR.FAR,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.FAST,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {
              pierce = PC.INFINITE, 
              ricochet = (self.level == 3 and 3 or 0)
            })
          end
        end, nil, nil, 'shoot')
    end
  },

  scout = {
    attack_range = AR.SHORT,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.SHORT)
      self.t:cooldown(CD.FAST,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {
              chain = (self.level == 3 and CC.LARGE or CC.MEDIUM)
            })
          end
        end, nil, nil, 'shoot')
    end
  },

  thief = {
    attack_range = AR.SHORT,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.SHORT)
      self.t:cooldown(CD.FAST,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {
              chain = (self.level == 3 and CC.HUGE or CC.NORMAL)
            })
          end
        end, nil, nil, 'shoot')
    end
  },

  -- WARRIOR 类角色
  swordsman = {
    attack_range = AR.CLOSE,
    cooldown = CD.NORMAL,
    attack_type = 'attack',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.CLOSE)
      self.t:cooldown(CD.NORMAL,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:attack(AR.MEDIUM)
        end, nil, nil, 'attack')
    end,
    level_bonuses = {
      [3] = function(self) self.swordsman_dmg_m = 2 end
    }
  },

  barbarian = {
    attack_range = AR.CLOSE,
    cooldown = CD.VERY_SLOW,
    attack_type = 'attack',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.CLOSE)
      self.t:cooldown(CD.VERY_SLOW,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:attack(AR.MEDIUM, {stun = 4})
        end, nil, nil, 'attack')
    end
  },

  juggernaut = {
    attack_range = AR.SHORT,
    cooldown = CD.VERY_SLOW,
    attack_type = 'attack',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.SHORT)
      self.t:cooldown(CD.VERY_SLOW,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:attack(AS.EXTREME, {juggernaut_push = true})
        end, nil, nil, 'attack')
    end
  },

  highlander = {
    attack_range = AR.MELEE,
    cooldown = CD.MEDIUM,
    attack_type = 'attack',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MELEE)
      self.t:cooldown(CD.MEDIUM,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          if self.level == 3 then
            self.t:every(0.25, function()
              self:attack(AS.LARGE)
            end, 3)
          else
            self:attack(AS.LARGE)
          end
        end, nil, nil, 'attack')
    end
  },

  -- MAGE 类角色
  wizard = {
    attack_range = AR.LONG,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.LONG)
      self.t:cooldown(CD.FAST,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {
              chain = (self.level == 3 and CC.SMALL or 0)
            })
          end
        end, nil, nil, 'shoot')
    end
  },

  elementor = {
    attack_range = AR.LONG,
    cooldown = CD.SLOW,
    attack_type = 'attack',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.LONG)
      self.t:cooldown(CD.SLOW,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local enemy = self:get_random_object_in_shape(self.attack_sensor, main.current.enemies)
          if enemy then
            self:attack(AS.EXTREME, {x = enemy.x, y = enemy.y})
          end
        end, nil, nil, 'attack')
    end
  },

  -- HEALER 类角色
  cleric = {
    attack_range = 0,
    cooldown = CD.VERY_SLOW,
    attack_type = 'heal',
    init = function(self)
      self.t:every(CD.VERY_SLOW, function()
        local orb_count = self.level == 3 and 4 or 1
        for i = 1, orb_count do
          local check_circle = Circle(
            random:float(main.current.x1 + 16, main.current.x2 - 16), 
            random:float(main.current.y1 + 16, main.current.y2 - 16), 
            2
          )
          local objects = main.current.main:get_objects_in_shape(check_circle, 
            {Seeker, EnemyCritter, Critter, Sentry, Automaton, Bomb, Volcano, Saboteur, Pet, Turret})
          while #objects > 0 do
            check_circle:move_to(
              random:float(main.current.x1 + 16, main.current.x2 - 16), 
              random:float(main.current.y1 + 16, main.current.y2 - 16)
            )
            objects = main.current.main:get_objects_in_shape(check_circle, 
              {Seeker, EnemyCritter, Critter, Sentry, Automaton, Bomb, Volcano, Saboteur, Pet, Turret})
          end
          SpawnEffect{
            group = main.current.effects, 
            x = check_circle.x, 
            y = check_circle.y, 
            color = green[0], 
            action = function(x, y)
              local check_circle = Circle(x, y, 2)
              local objects = main.current.main:get_objects_in_shape(check_circle, 
                {Seeker, EnemyCritter, Critter, Sentry, Automaton, Bomb, Volcano, Saboteur, Pet, Turret})
              if #objects == 0 then
                HealingOrb{group = main.current.main, x = x, y = y}
              end
            end
          }
        end
      end, nil, nil, 'heal')
    end
  },

  -- ROGUE 类角色
  assassin = {
    attack_range = AR.SHORT,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.SHORT)
      self.t:cooldown(CD.FAST,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {pierce = PC.INFINITE})
          end
        end, nil, nil, 'shoot')
    end
  },

  outlaw = {
    attack_range = AR.MEDIUM,
    cooldown = CD.NORMAL,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:cooldown(CD.NORMAL,
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {homing = (self.level == 3)})
          end
        end, nil, nil, 'shoot')
    end,
    level_bonuses = {
      [3] = function(self) self.outlaw_aspd_m = 1.5 end
    }
  },

  -- 特殊机制角色
  gambler = {
    attack_range = 0,
    cooldown = CD.FAST,
    attack_type = 'special',
    init = function(self)
      self.sorcerer_count = 0
      local cast = function(pitch_a)
        local enemy = table.shuffle(main.current.main:get_objects_by_classes(main.current.enemies))[1]
        if enemy then
          gambler1:play{pitch = pitch_a, volume = math.clamp(math.remap(gold, 0, 50, 0, 0.5), 0, 0.75)}
          enemy:hit(2*gold)
          -- Sorcerer相关逻辑
          if main.current.sorcerer_level > 0 then
            self.sorcerer_count = self.sorcerer_count + 1
            local threshold = (main.current.sorcerer_level == 3 and 2) or 
                            (main.current.sorcerer_level == 2 and 3) or 
                            (main.current.sorcerer_level == 1 and 4)
            if self.sorcerer_count >= threshold then
              self:sorcerer_repeat()
              self.sorcerer_count = 0
              self.t:after(0.25, function()
                local enemy = table.shuffle(main.current.main:get_objects_by_classes(main.current.enemies))[1]
                if enemy then
                  gambler1:play{pitch = pitch_a + 0.05, volume = math.clamp(math.remap(gold, 0, 50, 0, 0.5), 0, 0.75)}
                  enemy:hit(2*gold)
                end
              end)
            end
          end
        end
      end
      
      self.t:every(CD.FAST, function()
        cast(1)
        if self.level == 3 then
          -- 3级赌徒的连续攻击逻辑
          if random:bool(60) then
            if random:bool(40) then
              if random:bool(20) then
                self.t:after(0.25, function()
                  cast(1.1)
                  self.t:after(0.25, function()
                    cast(1.2)
                    self.t:after(0.25, function()
                      cast(1.3)
                    end)
                  end)
                end)
              else
                self.t:after(0.25, function()
                  cast(1.1)
                  self.t:after(0.25, function()
                    cast(1.2)
                  end)
                end)
              end
            else
              self.t:after(0.25, function()
                cast(1.1)
              end)
            end
          end
        end
      end, nil, nil, 'attack')
    end
  },

  -- MAGE 类角色
  magician = {
    attack_range = AR.MEDIUM,
    cooldown = CD.NORMAL,
    attack_type = 'magic',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:cooldown(CD.NORMAL, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          if self.magician_invulnerable then return end
          local enemy = self:get_random_object_in_shape(self.attack_sensor, main.current.enemies)
          if enemy then
            self:attack(32, {x = enemy.x, y = enemy.y})
          end
        end, nil, nil, 'attack')
      
      if self.level == 3 then
        self.t:every(12, function()
          self.magician_aspd_m = 1.5
          self.t:after(6, function() self.magician_aspd_m = 1 end, 'magician_aspd_m')
        end)
      end
    end
  },

  arcanist = {
    attack_range = AR.FAR,
    cooldown = 4,
    attack_type = 'shoot',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(4, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {pierce = 10000, v = 40})
            if main.current.sorcerer_level > 0 then
              self.sorcerer_count = self.sorcerer_count + 1
              if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
                self:sorcerer_repeat()
                self.sorcerer_count = 0
                self.t:after(0.25, function()
                  self:shoot(self:angle_to_object(closest_enemy), {pierce = 10000, v = 40})
                end)
              end
            end
          end
        end, nil, nil, 'shoot')
    end
  },

  artificer = {
    cooldown = 6,
    attack_type = 'spawn',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:every(6, function()
        SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
          artificer1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
          local check_circle = Circle(self.x, self.y, 2)
          local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Pet, Turret, Sentry, Bomb})
          if #objects == 0 then 
            Automaton{group = main.current.main, x = x, y = y, parent = self, level = self.level, conjurer_buff_m = self.conjurer_buff_m or 1}
          end
        end}
        
        if main.current.sorcerer_level > 0 then
          self.sorcerer_count = self.sorcerer_count + 1
          if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
            self:sorcerer_repeat()
            self.sorcerer_count = 0
            self.t:after(0.25, function()
              SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
                artificer1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
                local check_circle = Circle(self.x, self.y, 2)
                local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Pet, Turret, Sentry, Bomb})
                if #objects == 0 then 
                  Automaton{group = main.current.main, x = x, y = y, parent = self, level = self.level, conjurer_buff_m = self.conjurer_buff_m or 1}
                end
              end}
            end)
          end
        end
      end, nil, nil, 'spawn')
    end
  },

  blade = {
    attack_range = AR.NEAR,
    cooldown = CD.FAST,
    attack_type = 'blade',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.NEAR)
      self.t:cooldown(CD.FAST, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:shoot(random:float(0, 2*math.pi), {pierce = 1000})
        end, nil, nil, 'shoot')
    end
  },

  psychic = {
    attack_range = AR.FAR,
    cooldown = CD.NORMAL,
    attack_type = 'magic',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.NORMAL, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:attack(64, {stun = 4})
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          if enemies and #enemies > 0 then
            if self.psychosink then
              self.psychosink_dmg = self.psychosink_dmg + #enemies*self.dmg*(self.psychosink_dmg_m or 1)
            end
          end
          
          if main.current.sorcerer_level > 0 then
            self.sorcerer_count = self.sorcerer_count + 1
            if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
              self:sorcerer_repeat()
              self.sorcerer_count = 0
              self.t:after(0.25, function()
                self:attack(64, {stun = 4})
                if self.psychosink then
                  local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
                  if enemies then
                    self.psychosink_dmg = self.psychosink_dmg + #enemies*self.dmg*(self.psychosink_dmg_m or 1)
                  end
                end
              end)
            end
          end
        end, nil, nil, 'attack')
    end
  },

  saboteur = {
    cooldown = 8,
    attack_type = 'spawn',
    init = function(self)
      self.t:every(8, function()
        self.t:every(0.25, function()
          SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
            saboteur1:play{pitch = random:float(0.95, 1.05), volume = 0.2}
            Bomb{group = main.current.main, x = x, y = y, parent = self, level = self.level, speed_m = (main.current.chronomancer_level == 2 and 1.15) or 1}
          end}
        end, 2)
      end, nil, nil, 'spawn')
    end
  },

  bomber = {
    cooldown = 8,
    attack_type = 'spawn',
    init = function(self)
      self.t:every(8, function()
        SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
          Bomb{group = main.current.main, x = x, y = y, parent = self, level = self.level, speed_m = (main.current.chronomancer_level == 2 and 1.15) or 1}
        end}
      end, nil, nil, 'spawn')
    end
  },

  stormweaver = {
    attack_range = AR.FAR,
    cooldown = CD.SLOW,
    attack_type = 'chain',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.SLOW, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if enemy then
            self:shoot(self:angle_to_object(enemy), {chain = 7})
          end
          
          if main.current.sorcerer_level > 0 then
            self.sorcerer_count = self.sorcerer_count + 1
            if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
              self:sorcerer_repeat()
              self.sorcerer_count = 0
              self.t:after(0.25, function()
                local enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
                if enemy then
                  self:shoot(self:angle_to_object(enemy), {chain = 7})
                end
              end)
            end
          end
        end, nil, nil, 'shoot')
    end
  },

  sage = {
    attack_range = AR.FAR,
    cooldown = CD.SLOW,
    attack_type = 'shoot',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.SLOW, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if enemy then
            self:shoot(self:angle_to_object(enemy), {bounce = 3})
          end
          
          if main.current.sorcerer_level > 0 then
            self.sorcerer_count = self.sorcerer_count + 1
            if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
              self:sorcerer_repeat()
              self.sorcerer_count = 0
              self.t:after(0.25, function()
                local enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
                if enemy then
                  self:shoot(self:angle_to_object(enemy), {bounce = 3})
                end
              end)
            end
          end
        end, nil, nil, 'shoot')
    end
  },

  -- ROGUE 类角色
  cannoneer = {
    attack_range = AR.FAR,
    cooldown = CD.SLOW,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.SLOW, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {spawn_critters_on_kill = 7, spawn_critters_on_hit = (self.level == 3 and 3 or 0)})
          end
        end, nil, nil, 'shoot')
    end
  },

  vulcanist = {
    cooldown = 8,
    attack_type = 'spawn',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:every(8, function()
        local check_circle = Circle(self.x, self.y, 2)
        local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Pet, Turret, Sentry, Bomb, Automaton})
        if #objects == 0 then
          SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
            volcanist1:play{pitch = random:float(0.8, 1.2), volume = 0.4}
            Volcano{group = main.current.main, x = x, y = y, parent = self, level = self.level}
          end}
        end
        
        if main.current.sorcerer_level > 0 then
          self.sorcerer_count = self.sorcerer_count + 1
          if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
            self:sorcerer_repeat()
            self.sorcerer_count = 0
            self.t:after(0.25, function()
              local check_circle = Circle(self.x, self.y, 2)
              local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Pet, Turret, Sentry, Bomb, Automaton})
              if #objects == 0 then
                SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
                  volcanist1:play{pitch = random:float(0.8, 1.2), volume = 0.4}
                  Volcano{group = main.current.main, x = x, y = y, parent = self, level = self.level}
                end}
              end
            end)
          end
        end
      end, nil, nil, 'spawn')
    end
  },

  dual_gunner = {
    attack_range = AR.MEDIUM,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.dg_counter = 0
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:cooldown(CD.FAST, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            local r = self:angle_to_object(closest_enemy)
            self.dg_counter = self.dg_counter + 1
            dual_gunner1:play{pitch = random:float(0.95, 1.05), volume = 0.3}
            dual_gunner2:play{pitch = random:float(0.95, 1.05), volume = 0.3}
            if self.dg_counter == 5 or (self.dg_counter == 3 and self.level == 3) then
              self.dg_counter = 0
              for i = 1, 5 do
                self.t:after((i-1)*0.1, function()
                  self:shoot(r + random:float(-math.pi/32, math.pi/32), {knockback = (self.level == 3 and 14 or 7)})
                end)
              end
            else
              self:shoot(r, {})
            end
          end
        end, nil, nil, 'shoot')
    end
  },

  hunter = {
    attack_range = AR.FAR,
    cooldown = CD.NORMAL,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.NORMAL, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            hunter1:play{pitch = random:float(0.95, 1.05), volume = 0.3}
            HunterMark{group = main.current.main, x = closest_enemy.x, y = closest_enemy.y, target = closest_enemy, level = self.level}
          end
        end, nil, nil, 'shoot')
    end
  },

  chronomancer = {
    attack_range = AR.FAR,
    cooldown = CD.SLOW,
    attack_type = 'magic',
    init = function(self)
      self.aspd_m = 1.25
      if main.current.chronomancer_level == 2 then
        self.aspd_m = 1.35
      end
    end
  },

  spellblade = {
    attack_range = AR.NEAR,
    cooldown = CD.FAST,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.NEAR)
      self.t:cooldown(CD.FAST, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:shoot(random:float(0, 2*math.pi), {pierce = 1000})
        end, nil, nil, 'shoot')
    end
  },

  psykeeper = {
    attack_range = 0,
    cooldown = 0,
    attack_type = 'none',
    init = function(self)
      -- 无攻击
    end
  },

  engineer = {
    cooldown = 8,
    attack_type = 'spawn',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:every(8, function()
        SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
          sentry1:play{pitch = random:float(0.95, 1.05), volume = 0.3}
          Sentry{group = main.current.main, x = x, y = y, parent = self, level = self.level, conjurer_buff_m = self.conjurer_buff_m or 1}
        end}
        
        if self.level == 3 then
          self.t:every(0.5, function()
            self:attack(16, {x = self.x, y = self.y, pierce = 1000})
          end, nil, nil, 'attack')
        end
        
        if main.current.sorcerer_level > 0 then
          self.sorcerer_count = self.sorcerer_count + 1
          if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
            self:sorcerer_repeat()
            self.sorcerer_count = 0
            self.t:after(0.25, function()
              SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = self.color, action = function(x, y)
                sentry1:play{pitch = random:float(0.95, 1.05), volume = 0.3}
                Sentry{group = main.current.main, x = x, y = y, parent = self, level = self.level, conjurer_buff_m = self.conjurer_buff_m or 1}
              end}
            end)
          end
        end
      end, nil, nil, 'spawn')
    end
  },

  plague_doctor = {
    attack_range = AR.FAR,
    cooldown = CD.SLOW,
    attack_type = 'apply',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:every(CD.SLOW, function()
        local enemies = main.current.main:get_objects_by_classes(main.current.enemies)
        if enemies and #enemies > 0 then
          enemies = table.first(table.shuffle(enemies), 4)
          for _, enemy in ipairs(enemies) do
            enemy.bane_toxic = (self.level == 3 and 6 or 3)
            enemy.bane_toxic_owner = self
          end
        end
      end, nil, nil, 'apply')
    end
  },

  witch = {
    cooldown = 4,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(4, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local enemies = table.first(table.shuffle(self:get_objects_in_shape(self.attack_sensor, main.current.enemies)), 2)
          for _, enemy in ipairs(enemies) do
            enemy:curse('witch', self.level == 3 and 6 or 3, self)
          end
        end, nil, nil, 'curse')
    end
  },

  -- 辅助角色
  lich = {
    cooldown = 6,
    attack_type = 'spawn',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(6, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_random_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            lich_chain_1:play{pitch = random:float(0.9, 1.1), volume = 0.4}
            Chain{group = main.current.main, target = closest_enemy, parent = self, src = self, level = self.level}
            if self.level == 3 then
              local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
              enemies = table.select(enemies, function(v) return v.id ~= closest_enemy.id end)
              local enemy = random:table(enemies)
              if enemy then
                Chain{group = main.current.main, target = enemy, parent = self, src = self, level = self.level}
              end
            end
          end
        end, nil, nil, 'chain')
    end
  },

  cryomancer = {
    attack_range = AR.FAR,
    cooldown = 6,
    attack_type = 'slow',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:every(6, function()
        local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
        if enemies and #enemies > 0 then
          for _, enemy in ipairs(enemies) do
            freeze1:play{pitch = random:float(0.8, 1.2), volume = 0.3}
            enemy:slow(0.5, 2)
          end
        end
      end, nil, nil, 'slow')
    end
  },

  pyromancer = {
    attack_range = AR.FAR,
    cooldown = CD.NORMAL,
    attack_type = 'magic',
    init = function(self)
      self.dmg = 2*self.dmg
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.NORMAL, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          if enemies and #enemies > 0 then
            self:attack((self.level == 3 and 48 or 32), {burn = true})
          end
        end, nil, nil, 'attack')
    end
  },

  corruptor = {
    attack_range = AR.FAR,
    cooldown = CD.NORMAL,
    attack_type = 'magic',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.NORMAL, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          self:attack(64)
          
          if main.current.sorcerer_level > 0 then
            self.sorcerer_count = self.sorcerer_count + 1
            if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
              self:sorcerer_repeat()
              self.sorcerer_count = 0
              self.t:after(0.25, function()
                self:attack(64)
              end)
            end
          end
        end, nil, nil, 'attack')
    end
  },

  beastmaster = {
    attack_range = AR.MEDIUM,
    cooldown = CD.SLOW,
    attack_type = 'shoot',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.t:cooldown(CD.SLOW, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          if closest_enemy then
            self:shoot(self:angle_to_object(closest_enemy), {spawn_critters_on_kill = (self.level == 3 and 4 or 2)})
          end
        end, nil, nil, 'shoot')
    end
  },

  launcher = {
    attack_range = AR.FAR,
    cooldown = CD.SLOW,
    attack_type = 'shoot',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(CD.SLOW, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local target = self:get_random_object_in_shape(self.attack_sensor, main.current.enemies)
          if target then
            self:shoot(self:angle_to_object(target), {slow = (self.level >= 2), spawn_critters_on_crit = (self.level >= 2 and 4 or 0), homing = (self.level == 3 and target)})
            
            if main.current.sorcerer_level > 0 then
              self.sorcerer_count = self.sorcerer_count + 1
              if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
                self:sorcerer_repeat()
                self.sorcerer_count = 0
                self.t:after(0.25, function()
                  local target = self:get_random_object_in_shape(self.attack_sensor, main.current.enemies)
                  if target then
                    self:shoot(self:angle_to_object(target), {slow = (self.level >= 2), spawn_critters_on_crit = (self.level >= 2 and 4 or 0), homing = (self.level == 3 and target)})
                  end
                end)
              end
            end
          end
        end, nil, nil, 'shoot')
    end
  },

  -- CURSER 类角色
  jester = {
    attack_range = AR.MEDIUM,
    cooldown = 6,
    attack_type = 'curse',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.wide_attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(6, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          buff1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
          local enemies = table.first2(table.shuffle(self:get_objects_in_shape(self.wide_attack_sensor, main.current.enemies)),
            6 + ((self.malediction == 1 and 1) or (self.malediction == 2 and 3) or (self.malediction == 3 and 5) or 0) + 
            ((main.current.curser_level == 2 and 3) or (main.current.curser_level == 1 and 1) or 0))
          for _, enemy in ipairs(enemies) do
            if self:distance_to_object(enemy) < 128 then
              enemy:curse('jester', 6*(self.hex_duration_m or 1), self.level == 3, self)
              HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6, color = red[0], duration = 0.1}
              LightningLine{group = main.current.effects, src = self, dst = enemy, color = red[0]}
            end
          end
        end, nil, nil, 'attack')
    end
  },

  usurer = {
    attack_range = AR.MEDIUM,
    cooldown = 6,
    attack_type = 'curse',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.wide_attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(6, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          buff1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
          local enemies = table.first2(table.shuffle(self:get_objects_in_shape(self.wide_attack_sensor, main.current.enemies)),
            3 + ((self.malediction == 1 and 1) or (self.malediction == 2 and 3) or (self.malediction == 3 and 5) or 0) + 
            ((main.current.curser_level == 2 and 3) or (main.current.curser_level == 1 and 1) or 0))
          for _, enemy in ipairs(enemies) do
            enemy:curse('usurer', 10000, self.level == 3, self)
            enemy:apply_dot(self.dmg*(self.dot_dmg_m or 1)*(main.current.chronomancer_dot or 1), 10000)
            HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6, color = purple[0], duration = 0.1}
            LightningLine{group = main.current.effects, src = self, dst = enemy, color = purple[0]}
          end
        end, nil, nil, 'attack')
    end
  },

  silencer = {
    attack_range = AR.MEDIUM,
    cooldown = 6,
    attack_type = 'curse',
    init = function(self)
      self.sorcerer_count = 0
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.wide_attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(6, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local curse = function()
            buff1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
            local enemies = table.first2(table.shuffle(self:get_objects_in_shape(self.wide_attack_sensor, main.current.enemies)),
              6 + ((self.malediction == 1 and 1) or (self.malediction == 2 and 3) or (self.malediction == 3 and 5) or 0) + 
              ((main.current.curser_level == 2 and 3) or (main.current.curser_level == 1 and 1) or 0))
            for _, enemy in ipairs(enemies) do
              enemy:curse('silencer', 6*(self.hex_duration_m or 1), self.level == 3, self)
              if self.level == 3 then
                enemy:apply_dot(self.dmg*(self.dot_dmg_m or 1)*(main.current.chronomancer_dot or 1), 6*(self.hex_duration_m or 1))
              end
              HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6, color = blue2[0], duration = 0.1}
              LightningLine{group = main.current.effects, src = self, dst = enemy, color = blue2[0]}
            end
          end
          
          curse()
          
          if main.current.sorcerer_level > 0 then
            self.sorcerer_count = self.sorcerer_count + 1
            if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
              self.sorcerer_count = 0
              self:sorcerer_repeat()
              self.t:after(0.5, function()
                curse()
              end)
            end
          end
        end, nil, nil, 'attack')
    end
  },

  bane = {
    attack_range = AR.MEDIUM,
    cooldown = 6,
    attack_type = 'curse',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.wide_attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(6, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          buff1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
          local enemies = table.first2(table.shuffle(self:get_objects_in_shape(self.wide_attack_sensor, main.current.enemies)),
            6 + ((self.malediction == 1 and 1) or (self.malediction == 2 and 3) or (self.malediction == 3 and 5) or 0) + 
            ((main.current.curser_level == 2 and 3) or (main.current.curser_level == 1 and 1) or 0))
          for _, enemy in ipairs(enemies) do
            enemy:curse('bane', 6*(self.hex_duration_m or 1), self.level == 3, self)
            HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6, color = purple[0], duration = 0.1}
            LightningLine{group = main.current.effects, src = self, dst = enemy, color = purple[0]}
          end
        end, nil, nil, 'attack')
    end
  },

  -- SPAWN 类角色
  host = {
    cooldown = (function(self) return self.level == 3 and 1 or 2 end),
    attack_type = 'spawn',
    init = function(self)
      if self.level == 3 then
        self.t:every(1, function()
          critter1:play{pitch = random:float(0.95, 1.05), volume = 0.35}
          for i = 1, 2 do
            Critter{group = main.current.main, x = self.x, y = self.y, color = orange[0], 
              r = random:float(0, 2*math.pi), v = 10, dmg = self.dmg, parent = self}
          end
        end, nil, nil, 'spawn')
      else
        self.t:every(2, function()
          critter1:play{pitch = random:float(0.95, 1.05), volume = 0.35}
          Critter{group = main.current.main, x = self.x, y = self.y, color = orange[0], 
            r = random:float(0, 2*math.pi), v = 10, dmg = self.dmg, parent = self}
        end, nil, nil, 'spawn')
      end
    end
  },

  carver = {
    cooldown = 16,
    attack_type = 'spawn',
    init = function(self)
      self.t:every(16, function()
        Tree{group = main.current.main, x = self.x, y = self.y, color = self.color, parent = self, level = self.level}
      end, nil, nil, 'spawn')
    end
  },

  -- SPECIAL 类角色
  psykino = {
    attack_range = AR.FAR,
    cooldown = 4,
    attack_type = 'area',
    init = function(self)
      self.t:every(4, function()
        local center_enemy = self:get_random_object_in_shape(Circle(self.x, self.y, AR.FAR), main.current.enemies)
        if center_enemy then
          ForceArea{group = main.current.effects, x = center_enemy.x, y = center_enemy.y, 
            rs = self.area_size_m*64, color = self.color, character = self.character, level = self.level, parent = self}
        end
      end, nil, nil, 'attack')
    end
  },

  barrager = {
    attack_range = AR.FAR,
    cooldown = 4,
    attack_type = 'shoot',
    init = function(self)
      self.barrager_counter = 0
      self.attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(4, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          local closest_enemy = self:get_closest_object_in_shape(self.attack_sensor, main.current.enemies)
          local r = self:angle_to_object(closest_enemy)
          self.barrager_counter = self.barrager_counter + 1
          
          if self.barrager_counter == 3 and self.level == 3 then
            self.barrager_counter = 0
            for i = 1, 15 do
              self.t:after((i-1)*0.05, function()
                self:shoot(r + random:float(-math.pi/32, math.pi/32), {knockback = 14})
              end)
            end
          else
            for i = 1, 3 do
              self.t:after((i-1)*0.075, function()
                self:shoot(r + random:float(-math.pi/32, math.pi/32), {knockback = 7})
              end)
            end
          end
        end, nil, nil, 'shoot')
    end
  },

  -- HEALER 类角色
  fairy = {
    cooldown = 6,
    attack_type = 'heal',
    init = function(self)
      self.t:every(6, function()
        if self.level == 3 then
          -- 3级fairy给两个单位加速
          local units = self:get_all_units()
          local unit_1 = random:table(units)
          local runs = 0
          if unit_1 then
            while table.any(non_attacking_characters, function(v) return v == unit_1.character end) and runs < 1000 do 
              unit_1 = random:table(units)
              runs = runs + 1 
            end
          end
          
          local unit_2 = random:table(units)
          runs = 0
          if unit_2 then
            while table.any(non_attacking_characters, function(v) return v == unit_2.character end) and runs < 1000 do 
              unit_2 = random:table(units)
              runs = runs + 1 
            end
          end
          
          if unit_1 then
            unit_1.fairy_aspd_m = 3
            unit_1.fairyd = true
            unit_1.t:after(5.98, function() unit_1.fairy_aspd_m = 1; unit_1.fairyd = false end)
          end
          if unit_2 then
            unit_2.fairy_aspd_m = 3
            unit_2.fairyd = true
            unit_2.t:after(5.98, function() unit_2.fairy_aspd_m = 1; unit_2.fairyd = false end)
          end
          
          heal1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
          buff1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
          
          -- 生成2个治疗球
          for i = 1, 2 do
            local check_circle = Circle(random:float(main.current.x1 + 16, main.current.x2 - 16), 
              random:float(main.current.y1 + 16, main.current.y2 - 16), 2)
            local objects = main.current.main:get_objects_in_shape(check_circle, 
              {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Bomb, Pet, Turret, Sentry})
            while #objects > 0 do
              check_circle:move_to(random:float(main.current.x1 + 16, main.current.x2 - 16), 
                random:float(main.current.y1 + 16, main.current.y2 - 16))
              objects = main.current.main:get_objects_in_shape(check_circle, 
                {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Pet, Turret, Sentry, Bomb})
            end
            SpawnEffect{group = main.current.effects, x = check_circle.x, y = check_circle.y, color = green[0], 
              action = function(x, y)
                local check_circle = Circle(x, y, 2)
                local objects = main.current.main:get_objects_in_shape(check_circle, 
                  {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Bomb, Pet, Turret, Sentry})
                if #objects == 0 then
                  HealingOrb{group = main.current.main, x = x, y = y}
                end
              end}
          end
        else
          -- 1-2级fairy给一个单位加速
          local unit = random:table(self:get_all_units())
          local runs = 0
          while table.any(non_attacking_characters, function(v) return v == unit.character end) and runs < 1000 do 
            unit = random:table(self:get_all_units())
            runs = runs + 1 
          end
          
          if unit then
            unit.fairyd = true
            unit.fairy_aspd_m = 2
            unit.t:after(5.98, function() unit.fairy_aspd_m = 1; unit.fairyd = false end)
          end
          
          heal1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
          buff1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
          
          -- 生成1个治疗球
          local check_circle = Circle(random:float(main.current.x1 + 16, main.current.x2 - 16), 
            random:float(main.current.y1 + 16, main.current.y2 - 16), 2)
          local objects = main.current.main:get_objects_in_shape(check_circle, 
            {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Bomb, Pet, Turret, Sentry})
          while #objects > 0 do
            check_circle:move_to(random:float(main.current.x1 + 16, main.current.x2 - 16), 
              random:float(main.current.y1 + 16, main.current.y2 - 16))
            objects = main.current.main:get_objects_in_shape(check_circle, 
              {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Bomb, Pet, Turret, Sentry})
          end
          SpawnEffect{group = main.current.effects, x = check_circle.x, y = check_circle.y, color = green[0], 
            action = function(x, y)
              local check_circle = Circle(x, y, 2)
              local objects = main.current.main:get_objects_in_shape(check_circle, 
                {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Pet, Turret, Sentry, Bomb})
              if #objects == 0 then
                HealingOrb{group = main.current.main, x = x, y = y}
              end
            end}
        end
      end, nil, nil, 'heal')
    end
  },

  warden = {
    cooldown = 12,
    attack_type = 'buff',
    init = function(self)
      self.sorcerer_count = 0
      self.t:every(12, function()
        local ward = function()
          if self.level == 3 then
            -- 3级warden给两个单位力场
            local units = self:get_all_units()
            local unit_1 = random:table_remove(units)
            local unit_2 = random:table_remove(units)
            
            if unit_1 then
              illusion1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
              main.current.t:every_immediate(0.1, function()
                local check_circle = Circle(unit_1.x, unit_1.y, 6)
                local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter})
                if #objects == 0 then
                  ForceField{group = main.current.main, x = unit_1.x, y = unit_1.y, parent = unit_1}
                  main.current.t:cancel('warden_force_field_1')
                end
              end, nil, nil, 'warden_force_field_1')
            end
            
            if unit_2 then
              illusion1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
              main.current.t:every_immediate(0.1, function()
                local check_circle = Circle(unit_2.x, unit_2.y, 6)
                local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter})
                if #objects == 0 then
                  ForceField{group = main.current.main, x = unit_2.x, y = unit_2.y, parent = unit_2}
                  main.current.t:cancel('warden_force_field_2')
                end
              end, nil, nil, 'warden_force_field_2')
            end
          else
            -- 1-2级warden给一个单位力场
            local unit = random:table(self:get_all_units())
            if unit then
              illusion1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
              main.current.t:every_immediate(0.1, function()
                local check_circle = Circle(unit.x, unit.y, 6)
                local objects = main.current.main:get_objects_in_shape(check_circle, {Seeker, EnemyCritter})
                if #objects == 0 then
                  ForceField{group = main.current.main, x = unit.x, y = unit.y, parent = unit}
                  main.current.t:cancel('warden_force_field_0')
                end
              end, nil, nil, 'warden_force_field_0')
            end
          end
        end
        
        ward()
        
        if main.current.sorcerer_level > 0 then
          self.sorcerer_count = self.sorcerer_count + 1
          if self.sorcerer_count >= ((main.current.sorcerer_level == 3 and 2) or (main.current.sorcerer_level == 2 and 3) or (main.current.sorcerer_level == 1 and 4)) then
            self.sorcerer_count = 0
            self:sorcerer_repeat()
            self.t:after(0.5, function()
              ward()
            end)
          end
        end
      end, nil, nil, 'buff')
    end
  },

  priest = {
    cooldown = 12,
    attack_type = 'heal',
    init = function(self)
      if self.level == 3 then
        self.t:after(0.01, function()
          local all_units = self:get_all_units()
          local unit_1 = random:table_remove(all_units)
          local unit_2 = random:table_remove(all_units)
          local unit_3 = random:table_remove(all_units)
          if unit_1 then unit_1.divined = true end
          if unit_2 then unit_2.divined = true end
          if unit_3 then unit_3.divined = true end
        end)
      end
      
      self.t:every(12, function()
        local x, y = random:float(main.current.x1 + 16, main.current.x2 - 16), 
          random:float(main.current.y1 + 16, main.current.y2 - 16)
        for i = 1, 3 do
          SpawnEffect{group = main.current.effects, x = x, y = y, color = green[0], action = function(x, y)
            local check_circle = Circle(x, y, 2)
            local objects = main.current.main:get_objects_in_shape(check_circle, 
              {Seeker, EnemyCritter, Critter, Volcano, Saboteur, Bomb, Pet, Turret, Sentry, Automaton})
            if #objects == 0 then
              HealingOrb{group = main.current.main, x = x, y = y}
            end
          end}
        end
      end, nil, nil, 'heal')
    end
  },

  infestor = {
    attack_range = AR.MEDIUM,
    cooldown = 6,
    attack_type = 'curse',
    init = function(self)
      self.attack_sensor = Circle(self.x, self.y, AR.MEDIUM)
      self.wide_attack_sensor = Circle(self.x, self.y, AR.FAR)
      self.t:cooldown(6, 
        function() 
          local enemies = self:get_objects_in_shape(self.attack_sensor, main.current.enemies)
          return enemies and #enemies > 0 
        end,
        function()
          buff1:play{pitch = random:float(0.9, 1.1), volume = 0.5}
          local enemies = table.first2(table.shuffle(self:get_objects_in_shape(self.wide_attack_sensor, main.current.enemies)),
            8 + ((self.malediction == 1 and 1) or (self.malediction == 2 and 3) or (self.malediction == 3 and 5) or 0) + 
            ((main.current.curser_level == 2 and 3) or (main.current.curser_level == 1 and 1) or 0))
          for _, enemy in ipairs(enemies) do
            enemy:curse('infestor', 6*(self.hex_duration_m or 1), (self.level == 3 and 6 or 2), self.dmg, self)
            HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6, color = orange[0], duration = 0.1}
            LightningLine{group = main.current.effects, src = self, dst = enemy, color = orange[0]}
          end
        end, nil, nil, 'attack')
    end
  },

  flagellant = {
    cooldown = 8,
    attack_type = 'buff',
    init = function(self)
      self.t:every(8, function()
        buff1:play{pitch = random:float(0.95, 1.05), volume = 0.3}
        flagellant1:play{pitch = random:float(0.95, 1.05), volume = 0.4}
        local all_units = self:get_all_units()
        for _, unit in ipairs(all_units) do
          if unit.character == 'flagellant' then
            hit2:play{pitch = random:float(0.95, 1.05), volume = 0.4}
            unit:hit(self.level == 3 and unit.dmg or 2*unit.dmg)
          end
          if not unit.flagellant_dmg_m then
            unit.flagellant_dmg_m = 1
          end
          if self.level == 3 then
            unit.flagellant_dmg_m = unit.flagellant_dmg_m + 0.12
          else
            unit.flagellant_dmg_m = unit.flagellant_dmg_m + 0.04
          end
        end
      end, nil, nil, 'buff')
    end
  },
}

-- 辅助函数：初始化角色技能
local function init_character_skills(self)
  local skill_config = CHARACTER_SKILLS[self.character]
  if not skill_config then
    print("警告：未找到角色 " .. self.character .. " 的技能配置")
    return
  end
  
  -- 调用角色的初始化函数
  if skill_config.init then
    skill_config.init(self)
  end
  
  -- 应用等级加成
  if skill_config.level_bonuses and skill_config.level_bonuses[self.level] then
    skill_config.level_bonuses[self.level](self)
  end
end

-- 获取角色配置
local function get_character_config(character)
  return CHARACTER_SKILLS[character]
end

-- 导出接口
return {
  CHARACTER_SKILLS = CHARACTER_SKILLS,
  init_character_skills = init_character_skills,
  get_character_config = get_character_config,
}