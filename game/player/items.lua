--[[
模块：player/items.lua（可拾取物品类）
职责：
- 定义可被玩家拾取的物品对象
- Gold：金币掉落、磁力吸引、拾取奖励
- HealingOrb：治疗球掉落、磁力吸引、治疗效果
关键点：
- 物理交互：动态刚体、碰撞检测
- 磁力系统：根据玩家被动技能调整吸引范围
- 特效：拾取时的视觉和音频反馈
依赖：
- engine.game.*（GameObject/Physics）、main.current（游戏状态）、特效和音频资源
]]--

Gold = Object:extend()
Gold:implement(GameObject)
Gold:implement(Physics)
function Gold:init(args)
  self:init_game_object(args)
  if not self.group.world then self.dead = true; return end
  if tostring(self.x) == tostring(0/0) or tostring(self.y) == tostring(0/0) then self.dead = true; return end
  if #self.group:get_objects_by_class(Gold) > 30 then self.dead = true; return end
  self:set_as_rectangle(3, 3, 'dynamic', 'ghost')
  self:set_restitution(0.5)
  local r = random:float(0, 2*math.pi)
  local f = random:float(2, 4)
  self:apply_impulse(f*math.cos(r), f*math.sin(r))
  self:apply_angular_impulse(random:table{random:float(-6*math.pi, -2*math.pi), random:float(2*math.pi, 6*math.pi)})
  self:set_damping(2.5)
  self:set_angular_damping(5)
  self.color = yellow2[0]
  self.hfx:add('hit', 1)
  self.cant_be_picked_up = true
  self.t:after(0.5, function() self.cant_be_picked_up = false end)
  gold1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
  self.weak_magnet_sensor = Circle(self.x, self.y, 16)
  self.magnet_sensor = Circle(self.x, self.y, 56)
end


function Gold:update(dt)
  self:update_game_object(dt)
  self.r = self:get_angle()
  if not self.magnet_sensor then return end
  if not self.weak_magnet_sensor then return end

  local players = self:get_objects_in_shape(main.current.player.magnetism and self.magnet_sensor or self.weak_magnet_sensor, {Player})
  if players and #players > 0 then
    local x, y = 0, 0
    for _, p in ipairs(players) do
      x = x + p.x
      y = y + p.y
    end
    x = x/#players
    y = y/#players
    local r = self:angle_to_point(x, y)
    self:apply_force(20*math.cos(r), 20*math.sin(r))
  end
  if self.magnet_sensor then self.magnet_sensor:move_to(self.x, self.y) end
  if self.weak_magnet_sensor then self.weak_magnet_sensor:move_to(self.x, self.y) end
end


function Gold:draw()
  if not self.hfx.hit then return end
  graphics.push(self.x, self.y, self.r, self.hfx.hit.x, self.hfx.hit.x)
    graphics.rectangle(self.x, self.y, self.shape.w, self.shape.h, 1, 1, self.hfx.hit.f and fg[0] or self.color)
  graphics.pop()
end


function Gold:on_trigger_enter(other, contact)
  if self.cant_be_picked_up then return end

  if other:is(Player) then
    main.current.gold_picked_up = main.current.gold_picked_up + 1
    self.dead = true
    HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 4, color = fg[0], duration = 0.1}
    for i = 1, 2 do HitParticle{group = main.current.effects, x = self.x, y = self.y, color = self.color} end
    _G[random:table{'gold2', 'coins1', 'coins2', 'coins3'}]:play{pitch = random:float(0.9, 1.1), volume = 0.3}

    local units = other:get_all_units()
    local th
    for _, unit in ipairs(units) do
      if unit.character == 'miner' then
        th = unit
      end
    end
    if th then
      if th.level == 3 then
        trigger:after(0.01, function()
          if not main.current.main.world then return end
          _G[random:table{'scout1', 'scout2'}]:play{pitch = random:float(0.95, 1.05), volume = 0.35}
          HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6}
          local r = random:float(0, 2*math.pi)
          for i = 1, 8 do
            local t = {group = main.current.main, x = self.x + 8*math.cos(r), y = self.y + 8*math.sin(r), v = 250, r = r, color = yellow2[0], dmg = th.dmg, character = th.character, parent = th, level = th.level}
            Projectile(table.merge(t, mods or {}))
            r = r + math.pi/4
          end
        end)
      else
        trigger:after(0.01, function()
          if not main.current.main.world then return end
          _G[random:table{'scout1', 'scout2'}]:play{pitch = random:float(0.95, 1.05), volume = 0.35}
          HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 6}
          local r = random:float(0, 2*math.pi)
          for i = 1, 4 do
            local t = {group = main.current.main, x = self.x + 8*math.cos(r), y = self.y + 8*math.sin(r), v = 250, r = r, color = yellow2[0], dmg = th.dmg, character = th.character, parent = th, level = th.level}
            Projectile(table.merge(t, mods or {}))
            r = r + 2*math.pi/4
          end
        end)
      end
    end
  end
end




HealingOrb = Object:extend()
HealingOrb:implement(GameObject)
HealingOrb:implement(Physics)
function HealingOrb:init(args)
  self:init_game_object(args)
  if not self.group.world then self.dead = true; return end
  if tostring(self.x) == tostring(0/0) or tostring(self.y) == tostring(0/0) then self.dead = true; return end
  if #self.group:get_objects_by_class(HealingOrb) > 30 then self.dead = true; return end
  self:set_as_rectangle(4, 4, 'dynamic', 'ghost')
  self:set_restitution(0.5)
  local r = random:float(0, 2*math.pi)
  local f = random:float(2, 4)
  self:apply_impulse(f*math.cos(r), f*math.sin(r))
  self:apply_angular_impulse(random:table{random:float(-6*math.pi, -2*math.pi), random:float(2*math.pi, 6*math.pi)})
  self:set_damping(2.5)
  self:set_angular_damping(5)
  self.color = yellow2[0]
  self.hfx:add('hit', 1)
  self.cant_be_picked_up = true
  self.t:after(0.5, function() self.cant_be_picked_up = false end)
  illusion1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
  self.weak_magnet_sensor = Circle(self.x, self.y, 16)
  self.magnet_sensor = Circle(self.x, self.y, 56)

  if main.current.healer_level > 0 and not self.healer_effect_orb then
    if random:bool((main.current.healer_level == 1 and 15) or (main.current.healer_level == 2 and 30)) then
      SpawnEffect{group = main.current.effects, x = self.x, y = self.y, color = green[0], action = function(x, y)
        HealingOrb{group = main.current.main, x = x, y = y, healer_effect_orb = true}
      end}
    end
  end
end


function HealingOrb:update(dt)
  self:update_game_object(dt)
  self.r = self:get_angle()
  if not self.magnet_sensor then return end
  if not self.weak_magnet_sensor then return end

  local players = self:get_objects_in_shape(main.current.player.magnetism and self.magnet_sensor or self.weak_magnet_sensor, {Player})
  if players and #players > 0 then
    local x, y = 0, 0
    for _, p in ipairs(players) do
      x = x + p.x
      y = y + p.y
    end
    x = x/#players
    y = y/#players
    local r = self:angle_to_point(x, y)
    self:apply_force(20*math.cos(r), 20*math.sin(r))
  end
  if self.magnet_sensor then self.magnet_sensor:move_to(self.x, self.y) end
  if self.weak_magnet_sensor then self.weak_magnet_sensor:move_to(self.x, self.y) end
end


function HealingOrb:draw()
  if not self.hfx.hit then return end
  local sr = random:float(-0.1, 0.1)
  graphics.push(self.x, self.y, self.r, self.hfx.hit.x + sr, self.hfx.hit.x + sr)
    graphics.circle(self.x, self.y, 1.2*self.shape.w, self.hfx.hit.f and fg[0] or green_transparent_weak)
    graphics.circle(self.x, self.y, 0.5*self.shape.w, self.hfx.hit.f and fg[0] or green[0])
  graphics.pop()
end


function HealingOrb:on_trigger_enter(other, contact)
  if self.cant_be_picked_up then return end

  if other:is(Player) then
    self.dead = true
    HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 4, color = fg[0], duration = 0.1}
    for i = 1, 2 do HitParticle{group = main.current.effects, x = self.x, y = self.y, color = green[0]} end
    orb1:play{pitch = random:float(0.95, 1.05), volume = 1}
    heal1:play{pitch = random:float(0.95, 1.05), volume = 0.5}

    local units = other:get_all_units()
    local lowest_hp = 10
    local lowest_unit
    for _, unit in ipairs(units) do
      local r = unit.hp/unit.max_hp
      if r < lowest_hp then
        lowest_hp = r
        lowest_unit = unit
      end
    end
    if lowest_unit then
      lowest_unit:heal(0.2*lowest_unit.max_hp*(lowest_unit.heal_effect_m or 1))
    end

    if main.current.player.haste then
      local units = other:get_all_units()
      for _, unit in ipairs(units) do
        unit.hasted = love.timer.getTime()
        unit.t:after(4, function() unit.hasted = false end, 'haste')
      end
    end

    if main.current.player.divine_barrage and random:bool((main.current.player.divine_barrage == 1 and 20) or (main.current.player.divine_barrage == 2 and 40) or (main.current.player.divine_barrage == 3 and 60)) then
      trigger:after(0.01, function()
        if not main.current.main.world then return end
        main.current.player:barrage(main.current.player.r, 5, nil, 3)
      end)
    end
  end
end