--[[
模块：player_constants.lua
职责：定义Player相关的所有常量，包括攻击范围、冷却时间、伤害倍数等
目的：消除魔法数字，提高代码可读性和可维护性
]]--

-- 攻击范围常量
ATTACK_RANGE = {
  MELEE = 36,        -- 近战范围
  CLOSE = 48,        -- 近距离
  SHORT = 64,        -- 短距离
  MEDIUM = 96,       -- 中距离
  LONG = 128,        -- 长距离
  FAR = 160,         -- 远距离
  EXTREME = 512,     -- 极远距离（如psychic 3级）
}

-- 技能冷却时间（秒）
COOLDOWN = {
  VERY_FAST = 1,
  FAST = 2,
  NORMAL = 3,
  MEDIUM = 4,
  SLOW = 6,
  VERY_SLOW = 8,
  EXTREME_SLOW = 12,
  SPECIAL_SLOW = 16,
}

-- 伤害倍数
DAMAGE_MULTIPLIER = {
  HALF = 0.5,
  NORMAL = 1,
  DOUBLE = 2,
  CRIT_BASE = 4,
  CRIT_ROGUE_1 = 8,
  CRIT_ROGUE_2 = 10,
  CRIT_ROGUE_3 = 12,
  THIEF_CRIT = 10,
}

-- 弹幕数量
BARRAGE = {
  SMALL = 3,
  MEDIUM = 5,
  LARGE = 8,
  EXTREME = 15,
}

-- 投射物速度
PROJECTILE_SPEED = {
  VERY_SLOW = 25,   -- sage
  SLOW = 40,        -- arcanist
  MEDIUM = 140,     -- lich
  NORMAL = 200,     -- psyker
  FAST = 250,       -- 大多数投射物
  VERY_FAST = 300,  -- dual_gunner
}

-- 区域效果大小基准
AREA_SIZE = {
  TINY = 24,
  SMALL = 32,
  MEDIUM = 42,
  NORMAL = 48,
  LARGE = 64,
  VERY_LARGE = 72,
  HUGE = 96,
  EXTREME = 128,
}

-- Buff持续时间
BUFF_DURATION = {
  SHORT = 2,
  MEDIUM = 4,
  NORMAL = 6,
  LONG = 8,
  VERY_LONG = 10,
  EXTREME = 12,
  PERMANENT = 10000,
}

-- 击退力度
KNOCKBACK_FORCE = {
  LIGHT = 7,
  MEDIUM = 14,
  NORMAL = 25,
  STRONG = 35,
  VERY_STRONG = 50,
}

-- 连锁/弹射次数
CHAIN_COUNT = {
  SMALL = 2,
  MEDIUM = 3,
  NORMAL = 5,
  LARGE = 6,
  VERY_LARGE = 7,
  HUGE = 10,
  EXTREME = 14,
}

-- 穿透次数
PIERCE_COUNT = {
  NORMAL = 1,
  INFINITE = 1000,
  MAX = 10000,
}

-- 暴击率
CRIT_CHANCE = {
  LOW = 10,
  NORMAL = 15,
  HIGH = 30,
  GUARANTEED = 100,
}

-- 音效配置
SOUND_EFFECTS = {
  -- 射击音效
  shoot = {
    vagrant = 'shoot1',
    wizard = 'wizard1',
    archer = 'archer1',
    scout = {'scout1', 'scout2'},
    dual_gunner = {'dual_gunner1', 'dual_gunner2'},
    cannoneer = {'cannoneer1', 'cannoneer2'},
  },
  
  -- 近战音效
  melee = {
    swordsman = {'swordsman1', 'swordsman2'},
    barbarian = {'swordsman1', 'swordsman2'},
    juggernaut = {'swordsman1', 'swordsman2', 'elementor1'},
    highlander = {'swordsman1', 'swordsman2'},
  },
  
  -- 施法音效
  cast = {
    elementor = 'elementor1',
    psychic = 'psychic1',
    launcher = 'buff1',
    witch = 'dot1',
    lich = 'frost1',
    arcanist = 'arcane1',
  },
  
  -- 召唤音效
  summon = {
    artificer = 'artificer1',
    host = 'critter1',
    bomber = 'spawn1',
  },
  
  -- 治疗音效
  heal = {
    cleric = 'heal1',
    priest = 'heal1',
    fairy = {'heal1', 'buff1'},
  },
}

-- 音效音量
SOUND_VOLUME = {
  VERY_LOW = 0.1,
  LOW = 0.2,
  MEDIUM = 0.3,
  NORMAL = 0.35,
  HIGH = 0.4,
  VERY_HIGH = 0.5,
  MAX = 0.75,
}

-- 相机震动强度
CAMERA_SHAKE = {
  LIGHT = 2,
  NORMAL = 4,
  STRONG = 6,
}

-- 形状大小
SHAPE_SIZE = {
  UNIT = 9,         -- 单位基础大小
  SMALL_MULT = 0.8, -- 小型化倍数
  NORMAL_MULT = 1,  -- 正常倍数
  LARGE_MULT = 1.25,-- 放大倍数
  HUGE_MULT = 1.6,  -- 巨大化倍数
}

-- HP相关常量
HP_CONSTANTS = {
  HEAL_ORB_AMOUNT = 0.2,    -- 治疗球回复比例
  PSYKEEPER_THRESHOLD = 0.25,-- psykeeper触发阈值
  DIVINED_REVIVE = 1.0,      -- 神佑复活回复比例
  FLAGELLANT_SELF_DAMAGE = 0.05, -- flagellant自伤比例
  DEATH_6_DAMAGE = 0.1,      -- death_6被动伤害比例
}

-- 移动速度倍率
MOVE_SPEED_MULT = {
  SLOW = 0.8,
  NORMAL = 1.0,
  FAST = 1.1,
  VERY_FAST = 1.2,
  EXTREME = 1.3,
  WALL_RIDER = 1.25,
  HASTE_MAX = 1.5,
}

-- 攻速倍率
ATTACK_SPEED_MULT = {
  SLOW = 0.8,
  NORMAL = 1.0,
  FAST = 1.15,
  VERY_FAST = 1.2,
  EXTREME_FAST = 1.35,
  DOUBLE = 2.0,
  TRIPLE = 3.0,
}

return {
  ATTACK_RANGE = ATTACK_RANGE,
  COOLDOWN = COOLDOWN,
  DAMAGE_MULTIPLIER = DAMAGE_MULTIPLIER,
  BARRAGE = BARRAGE,
  PROJECTILE_SPEED = PROJECTILE_SPEED,
  AREA_SIZE = AREA_SIZE,
  BUFF_DURATION = BUFF_DURATION,
  KNOCKBACK_FORCE = KNOCKBACK_FORCE,
  CHAIN_COUNT = CHAIN_COUNT,
  PIERCE_COUNT = PIERCE_COUNT,
  CRIT_CHANCE = CRIT_CHANCE,
  SOUND_EFFECTS = SOUND_EFFECTS,
  SOUND_VOLUME = SOUND_VOLUME,
  CAMERA_SHAKE = CAMERA_SHAKE,
  SHAPE_SIZE = SHAPE_SIZE,
  HP_CONSTANTS = HP_CONSTANTS,
  MOVE_SPEED_MULT = MOVE_SPEED_MULT,
  ATTACK_SPEED_MULT = ATTACK_SPEED_MULT,
}