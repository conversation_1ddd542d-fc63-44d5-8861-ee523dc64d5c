--[[
模块：media.lua（媒体/演示状态）
职责：
- 用于媒体/展示的状态：构建 main/effects/ui 分组，设置背景与文本元素
- 非核心玩法逻辑，但用于资源/图形演示与调试
关键点：
- on_enter：初始化相机与分组，并绘制标题/说明文本
- update/draw：按照分组顺序进行更新与绘制
性能注意：
- 状态空转时也会更新，避免在 update/draw 中分配临时表
依赖：
- shared（字体/颜色）、engine.game.*（Group/State）
]]--

Media = Object:extend()
Media:implement(State)
function Media:init(name)
  self:init_state(name)
end


function Media:on_enter(from)
  camera.x, camera.y = gw/2, gh/2
  self.main = Group()
  self.effects = Group()
  self.ui = Group()

  graphics.set_background_color(blue[0])
  Text2{group = self.ui, x = gw/2, y = gh/2, lines = {
    {text = '[fg]SNKRX', font = fat_font, alignment = 'center', height_offset = -15},
    {text = '[fg]loop update', font = pixul_font, alignment = 'center'},
  }}
end


function Media:update(dt)
  self.main:update(dt*slow_amount)
  self.effects:update(dt*slow_amount)
  self.ui:update(dt*slow_amount)
end


function Media:draw()
  self.main:draw()
  self.effects:draw()
  self.ui:draw()

  mercenary:draw(30, 30, 0, 1, 1, 0, 0, yellow2[-5])
end
