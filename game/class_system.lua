--[[
模块：class_system.lua（职业系统管理）
职责：
- 定义非攻击角色和无冷却角色列表
- 提供职业统计和计算相关函数
- 管理职业等级和数量统计
- 处理职业系统的核心逻辑
依赖：character_data.lua（角色职业数据）
]]--

-- 非攻击角色列表（支持和辅助型角色）
non_attacking_characters = {'cleric', 'stormweaver', 'squire', 'chronomancer', 'sage', 'psykeeper', 'bane', 'carver', 'fairy', 'priest', 'flagellant', 'merchant', 'miner'}

-- 非冷却角色列表（被动效果型角色）
non_cooldown_characters = {'squire', 'chronomancer', 'psykeeper', 'merchant', 'miner'}

-- 功能：统计单位列表中各职业的数量
-- 参数：units(table) - 单位列表，每个单位需有character字段
-- 返回：table - 各职业数量的映射表
get_number_of_units_per_class = function(units)
  local rangers = 0
  local warriors = 0
  local healers = 0
  local mages = 0
  local nukers = 0
  local conjurers = 0
  local rogues = 0
  local enchanters = 0
  local psykers = 0
  local cursers = 0
  local forcers = 0
  local swarmers = 0
  local voiders = 0
  local sorcerers = 0
  local mercenaries = 0
  local explorers = 0
  for _, unit in ipairs(units) do
    for _, unit_class in ipairs(character_classes[unit.character]) do
      if unit_class == 'ranger' then rangers = rangers + 1 end
      if unit_class == 'warrior' then warriors = warriors + 1 end
      if unit_class == 'healer' then healers = healers + 1 end
      if unit_class == 'mage' then mages = mages + 1 end
      if unit_class == 'nuker' then nukers = nukers + 1 end
      if unit_class == 'conjurer' then conjurers = conjurers + 1 end
      if unit_class == 'rogue' then rogues = rogues + 1 end
      if unit_class == 'enchanter' then enchanters = enchanters + 1 end
      if unit_class == 'psyker' then psykers = psykers + 1 end
      if unit_class == 'curser' then cursers = cursers + 1 end
      if unit_class == 'forcer' then forcers = forcers + 1 end
      if unit_class == 'swarmer' then swarmers = swarmers + 1 end
      if unit_class == 'voider' then voiders = voiders + 1 end
      if unit_class == 'sorcerer' then sorcerers = sorcerers + 1 end
      if unit_class == 'mercenary' then mercenaries = mercenaries + 1 end
      if unit_class == 'explorer' then explorers = explorers + 1 end
    end
  end
  return {ranger = rangers, warrior = warriors, healer = healers, mage = mages, nuker = nukers, conjurer = conjurers, rogue = rogues,
    enchanter = enchanters, psyker = psykers, curser = cursers, forcer = forcers, swarmer = swarmers, voider = voiders, sorcerer = sorcerers, mercenary = mercenaries, explorer = explorers}
end

-- 功能：根据单位数量计算各职业的等级
-- 参数：units(table) - 单位列表
-- 返回：table - 各职业等级的映射表
get_class_levels = function(units)
  local units_per_class = get_number_of_units_per_class(units)
  local units_to_class_level = function(number_of_units, class)
    if class == 'ranger' or class == 'warrior' or class == 'mage' or class == 'nuker' or class == 'rogue' then
      if number_of_units >= 6 then return 2
      elseif number_of_units >= 3 then return 1
      else return 0 end
    elseif class == 'healer' or class == 'conjurer' or class == 'enchanter' or class == 'curser' or class == 'forcer' or class == 'swarmer' or class == 'voider' or class == 'mercenary' or class == 'psyker' then
      if number_of_units >= 4 then return 2
      elseif number_of_units >= 2 then return 1
      else return 0 end
    elseif class == 'sorcerer' then
      if number_of_units >= 6 then return 3
      elseif number_of_units >= 4 then return 2
      elseif number_of_units >= 2 then return 1
      else return 0 end
    elseif class == 'explorer' then
      if number_of_units >= 1 then return 1
      else return 0 end
    end
  end
  return {
    ranger = units_to_class_level(units_per_class.ranger, 'ranger'),
    warrior = units_to_class_level(units_per_class.warrior, 'warrior'),
    mage = units_to_class_level(units_per_class.mage, 'mage'),
    nuker = units_to_class_level(units_per_class.nuker, 'nuker'),
    rogue = units_to_class_level(units_per_class.rogue, 'rogue'),
    healer = units_to_class_level(units_per_class.healer, 'healer'),
    conjurer = units_to_class_level(units_per_class.conjurer, 'conjurer'),
    enchanter = units_to_class_level(units_per_class.enchanter, 'enchanter'),
    psyker = units_to_class_level(units_per_class.psyker, 'psyker'),
    curser = units_to_class_level(units_per_class.curser, 'curser'),
    forcer = units_to_class_level(units_per_class.forcer, 'forcer'),
    swarmer = units_to_class_level(units_per_class.swarmer, 'swarmer'),
    voider = units_to_class_level(units_per_class.voider, 'voider'),
    sorcerer = units_to_class_level(units_per_class.sorcerer, 'sorcerer'),
    mercenary = units_to_class_level(units_per_class.mercenary, 'mercenary'),
    explorer = units_to_class_level(units_per_class.explorer, 'explorer'),
  }
end

-- 功能：获取单位列表中所有激活的职业类型
-- 参数：units(table) - 单位列表
-- 返回：table - 去重后的职业类型列表
get_classes = function(units)
  local classes = {}
  for _, unit in ipairs(units) do
    table.insert(classes, table.copy(character_classes[unit.character]))
  end
  return table.unify(table.flatten(classes))
end

-- 职业颜色映射表（延迟初始化，依赖于颜色变量）
-- 功能：初始化职业颜色映射
-- 参数：无
-- 返回：无
function init_class_colors()
  class_colors = {
    ['warrior'] = yellow[0],
    ['ranger'] = green[0],
    ['healer'] = green[0],
    ['conjurer'] = orange[0],
    ['mage'] = blue[0],
    ['nuker'] = red[0],
    ['rogue'] = red[0],
    ['enchanter'] = blue[0],
    ['psyker'] = fg[0],
    ['curser'] = purple[0],
    ['forcer'] = yellow[0],
    ['swarmer'] = orange[0],
    ['voider'] = purple[0],
    ['sorcerer'] = blue2[0],
    ['mercenary'] = yellow2[0],
    ['explorer'] = fg[0],
  }

  class_color_strings = {
    ['warrior'] = 'yellow',
    ['ranger'] = 'green',
    ['healer'] = 'green',
    ['conjurer'] = 'orange',
    ['mage'] = 'blue',
    ['nuker'] = 'red',
    ['rogue'] = 'red',
    ['enchanter'] = 'blue',
    ['psyker'] = 'fg',
    ['curser'] = 'purple',
    ['forcer'] = 'yellow',
    ['swarmer'] = 'orange',
    ['voider'] = 'purple',
    ['sorcerer'] = 'blue2',
    ['mercenary'] = 'yellow2',
    ['explorer'] = 'fg',
  }
end

-- 职业集合数量要求定义（用于UI显示和效果激活）
class_set_numbers = {
  ['ranger'] = function(units) return 3, 6, nil, get_number_of_units_per_class(units).ranger end,
  ['warrior'] = function(units) return 3, 6, nil, get_number_of_units_per_class(units).warrior end,
  ['mage'] = function(units) return 3, 6, nil, get_number_of_units_per_class(units).mage end,
  ['nuker'] = function(units) return 3, 6, nil, get_number_of_units_per_class(units).nuker end,
  ['rogue'] = function(units) return 3, 6, nil, get_number_of_units_per_class(units).rogue end,
  ['healer'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).healer end,
  ['conjurer'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).conjurer end,
  ['enchanter'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).enchanter end,
  ['psyker'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).psyker end,
  ['curser'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).curser end,
  ['forcer'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).forcer end,
  ['swarmer'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).swarmer end,
  ['voider'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).voider end,
  ['sorcerer'] = function(units) return 2, 4, 6, get_number_of_units_per_class(units).sorcerer end,
  ['mercenary'] = function(units) return 2, 4, nil, get_number_of_units_per_class(units).mercenary end,
  ['explorer'] = function(units) return 1, 1, nil, get_number_of_units_per_class(units).explorer end,
}