--[[
模块：passive_data.lua（被动技能数据管理）
职责：
- 定义所有被动技能的名称、描述和等级描述
- 管理不可升级的道具列表
- 提供被动技能相关的显示信息
依赖：shared.lua（颜色定义），character_data.lua（角色属性函数）
]]--

-- 被动技能名称定义
passive_names = {
  ['centipede'] = 'Centipede',
  ['ouroboros_technique_r'] = 'Ouroboros Technique R',
  ['ouroboros_technique_l'] = 'Ouroboros Technique L',
  ['amplify'] = 'Amplify',
  ['resonance'] = 'Resonance',
  ['ballista'] = 'Ballista',
  ['call_of_the_void'] = 'Call of the Void',
  ['crucio'] = 'Crucio',
  ['speed_3'] = 'Speed 3',
  ['damage_4'] = 'Damage 4',
  ['shoot_5'] = 'Shoot 5',
  ['death_6'] = 'Death 6',
  ['lasting_7'] = 'Lasting 7',
  ['defensive_stance'] = 'Defensive Stance',
  ['offensive_stance'] = 'Offensive Stance',
  ['kinetic_bomb'] = 'Kinetic Bomb',
  ['porcupine_technique'] = 'Porcupine Technique',
  ['last_stand'] = 'Last Stand',
  ['seeping'] = 'Seeping',
  ['deceleration'] = 'Deceleration',
  ['annihilation'] = 'Annihilation',
  ['malediction'] = 'Malediction',
  ['hextouch'] = 'Hextouch',
  ['whispers_of_doom'] = 'Whispers of Doom',
  ['tremor'] = 'Tremor',
  ['heavy_impact'] = 'Heavy Impact',
  ['fracture'] = 'Fracture',
  ['meat_shield'] = 'Meat Shield',
  ['hive'] = 'Hive',
  ['baneling_burst'] = 'Baneling Burst',
  ['blunt_arrow'] = 'Blunt Arrow',
  ['explosive_arrow'] = 'Explosive Arrow',
  ['divine_machine_arrow'] = 'Divine Machine Arrow',
  ['chronomancy'] = 'Chronomancy',
  ['awakening'] = 'Awakening',
  ['divine_punishment'] = 'Divine Punishment',
  ['assassination'] = 'Assassination',
  ['flying_daggers'] = 'Flying Daggers',
  ['ultimatum'] = 'Ultimatum',
  ['magnify'] = 'Magnify',
  ['echo_barrage'] = 'Echo Barrage',
  ['unleash'] = 'Unleash',
  ['reinforce'] = 'Reinforce',
  ['payback'] = 'Payback',
  ['enchanted'] = 'Enchanted',
  ['freezing_field'] = 'Freezing Field',
  ['burning_field'] = 'Burning Field',
  ['gravity_field'] = 'Gravity Field',
  ['magnetism'] = 'Magnetism',
  ['insurance'] = 'Insurance',
  ['dividends'] = 'Dividends',
  ['berserking'] = 'Berserking',
  ['unwavering_stance'] = 'Unwavering Stance',
  ['unrelenting_stance'] = 'Unrelenting Stance',
  ['blessing'] = 'Blessing',
  ['haste'] = 'Haste',
  ['divine_barrage'] = 'Divine Barrage',
  ['orbitism'] = 'Orbitism',
  ['psyker_orbs'] = 'Psyker Orbs',
  ['psychosense'] = 'Psychosense',
  ['psychosink'] = 'Psychosink',
  ['rearm'] = 'Rearm',
  ['taunt'] = 'Taunt',
  ['construct_instability'] = 'Construct Instability',
  ['intimidation'] = 'Intimidation',
  ['vulnerability'] = 'Vulnerability',
  ['temporal_chains'] = 'Temporal Chains',
  ['ceremonial_dagger'] = 'Ceremonial Dagger',
  ['homing_barrage'] = 'Homing Barrage',
  ['critical_strike'] = 'Critical Strike',
  ['noxious_strike'] = 'Noxious Strike',
  ['infesting_strike'] = 'Infesting Strike',
  ['kinetic_strike'] = 'Kinetic Strike',
  ['burning_strike'] = 'Burning Strike',
  ['lucky_strike'] = 'Lucky Strike',
  ['healing_strike'] = 'Healing Strike',
  ['stunning_strike'] = 'Stunning Strike',
  ['silencing_strike'] = 'Silencing Strike',
  ['warping_shots'] = 'Warping Shots',
  ['culling_strike'] = 'Culling Strike',
  ['lightning_strike'] = 'Lightning Strike',
  ['psycholeak'] = 'Psycholeak',
  ['divine_blessing'] = 'Divine Blessing',
  ['hardening'] = 'Hardening',
}

-- 被动技能描述定义（基础版本）
passive_descriptions = {
  ['centipede'] = '[yellow]+10/20/30%[fg] movement speed',
  ['ouroboros_technique_r'] = '[fg]rotating around yourself to the right releases [yellow]2/3/4[fg] projectiles per second',
  ['ouroboros_technique_l'] = '[fg]rotating around yourself to the left grants [yellow]+15/25/35%[fg] defense to all units',
  ['amplify'] = '[yellow]+20/35/50%[fg] AoE damage',
  ['resonance'] = '[fg]all AoE attacks deal [yellow]+3/5/7%[fg] damage per unit hit',
  ['ballista'] = '[yellow]+20/35/50%[fg] projectile damage',
  ['call_of_the_void'] = '[yellow]+30/60/90%[fg] DoT damage',
  ['crucio'] = '[fg]taking damage also shares that across all enemies at [yellow]20/30/40%[fg] its value',
  ['speed_3'] = '[fg]position [yellow]3[fg] has [yellow]+50%[fg] attack speed',
  ['damage_4'] = '[fg]position [yellow]4[fg] has [yellow]+30%[fg] damage',
  ['shoot_5'] = '[fg]position [yellow]5[fg] shoots [yellow]3[fg] projectiles per second',
  ['death_6'] = '[fg]position [yellow]6[fg] takes [yellow]10%[fg] of its health as damage every [yellow]3[fg] seconds',
  ['lasting_7'] = '[fg]position [yellow]7[fg] will stay alive for [yellow]10[fg] seconds after dying',
  ['defensive_stance'] = '[fg]first and last positions have [yellow]+10/20/30%[fg] defense',
  ['offensive_stance'] = '[fg]first and last positions have [yellow]+10/20/30%[fg] damage',
  ['kinetic_bomb'] = '[fg]when an ally dies it explodes, launching enemies away',
  ['porcupine_technique'] = '[fg]when an ally dies it explodes, releasing piercing and ricocheting projectiles',
  ['last_stand'] = '[fg]the last unit alive is fully healed and receives a [yellow]+20%[fg] bonus to all stats',
  ['seeping'] = '[fg]enemies taking DoT damage have [yellow]-15/25/35%[fg] defense',
  ['deceleration'] = '[fg]enemies taking DoT damage have [yellow]-15/25/35%[fg] movement speed',
  ['annihilation'] = '[fg]when a voider dies deal its DoT damage to all enemies for [yellow]3[fg] seconds',
  ['malediction'] = '[yellow]+1/3/5[fg] max curse targets to all allied cursers',
  ['hextouch'] = '[fg]enemies take [yellow]10/15/20[fg] damage per second for [yellow]3[fg] seconds when cursed',
  ['whispers_of_doom'] = '[fg]curses apply doom, deal [yellow]100/150/200[fg] damage every [yellow]4/3/2[fg] doom instances',
  ['tremor'] = '[fg]when enemies hit walls they create an area based on the knockback force',
  ['heavy_impact'] = '[fg]when enemies hit walls they take damage based on the knockback force',
  ['fracture'] = '[fg]when enemies hit walls they explode into projectiles',
  ['meat_shield'] = '[fg]critters [yellow]block[fg] enemy projectiles',
  ['hive'] = '[fg]critters have [yellow]+1/2/3[fg] HP',
  ['baneling_burst'] = '[fg]critters die immediately on contact but also deal [yellow]50/100/150[fg] AoE damage',
  ['blunt_arrow'] = '[fg]ranger arrows have [yellow]+10/20/30%[fg] chance to knockback',
  ['explosive_arrow'] = '[fg]ranger arrows have [yellow]+10/20/30%[fg] chance to deal [yellow]10/20/30%[fg] AoE damage',
  ['divine_machine_arrow'] = '[fg]ranger arrows have a [yellow]10/20/30%[fg] chance to seek and pierce [yellow]1/2/3[fg] times',
  ['chronomancy'] = '[fg]mages cast their spells [yellow]15/25/35%[fg] faster',
  ['awakening'] = '[yellow]+50/75/100%[fg] attack speed and damage to [yellow]1[fg] mage every round for that round',
  ['divine_punishment'] = '[fg]deal damage to all enemies based on how many mages you have',
  ['assassination'] = '[fg]crits from rogues deal [yellow]8/10/12x[fg] damage but normal attacks deal [yellow]half[fg] damage',
  ['flying_daggers'] = '[fg]all projectiles thrown by rogues chain [yellow]+2/3/4[fg] times',
  ['ultimatum'] = '[fg]projectiles that chain gain [yellow]+10/20/30%[fg] damage with each chain',
  ['magnify'] = '[yellow]+20/35/50%[fg] area size',
  ['echo_barrage'] = '[yellow]10/20/30%[fg] chance to create [yellow]1/2/3[fg] secondary AoEs on AoE hit',
  ['unleash'] = '[fg]all nukers gain [yellow]+1%[fg] area size and damage every second',
  ['reinforce'] = '[yellow]+10/20/30%[fg] global damage, defense and aspd if you have one or more enchanters',
  ['payback'] = '[yellow]+2/5/8%[fg] damage to all allies whenever an enchanter is hit',
  ['enchanted'] = '[yellow]+33/66/99%[fg] attack speed to a random unit if you have two or more enchanters',
  ['freezing_field'] = '[fg]creates an area that slows enemies by [yellow]50%[fg] for [yellow]2[fg] seconds on sorcerer spell repeat',
  ['burning_field'] = '[fg]creates an area that deals [yellow]30[fg] dps for [yellow]2[fg] seconds on sorcerer spell repeat',
  ['gravity_field'] = '[fg]creates an area that pulls enemies in for [yellow]1[fg] seconds on sorcerer spell repeat',
  ['magnetism'] = '[fg]gold coins and healing orbs are attracted to the snake from a longer range',
  ['insurance'] = "[fg]heroes have [yellow]4[fg] times the chance of mercenary's bonus to drop [yellow]2[fg] gold on death",
  ['dividends'] = '[fg]mercenaries deal [yellow]+X%[fg] damage, where X is how much gold you have',
  ['berserking'] = '[fg]all warriors have up to [yellow]+50/75/100%[fg] attack speed based on missing HP',
  ['unwavering_stance'] = '[fg]all warriors gain [yellow]+4/8/12%[fg] defense every [yellow]5[fg] seconds',
  ['unrelenting_stance'] = '[yellow]+2/5/8%[fg] defense to all allies whenever a warrior is hit',
  ['blessing'] = '[yellow]+10/20/30%[fg] healing effectiveness',
  ['haste'] = '[yellow]+50%[fg] movement speed that decays over [yellow]4[fg] seconds on healing orb pick up',
  ['divine_barrage'] = '[yellow]20/40/60%[fg] chance to release a ricocheting barrage on healing orb pick up',
  ['orbitism'] = '[yellow]+25/50/75%[fg] psyker orb movement speed',
  ['psyker_orbs'] = '[yellow]+1/2/4[fg] total psyker orbs',
  ['psychosense'] = '[yellow]+33/66/99%[fg] orb range',
  ['psychosink'] = '[fg]psyker orbs deal [yellow]+40/80/120%[fg] damage',
  ['rearm'] = '[fg]constructs repeat their attacks once',
  ['taunt'] = '[yellow]10/20/30%[fg] chance for constructs to taunt nearby enemies on attack',
  ['construct_instability'] = '[fg]constructs explode when disappearing, dealing [yellow]100/150/200%[fg] damage',
  ['intimidation'] = '[fg]enemies spawn with [yellow]-10/20/30%[fg] max HP',
  ['vulnerability'] = '[fg]enemies take [yellow]+10/20/30%[fg] damage',
  ['temporal_chains'] = '[fg]enemies are [yellow]10/20/30%[fg] slower',
  ['ceremonial_dagger'] = '[fg]killing an enemy fires a homing dagger',
  ['homing_barrage'] = '[yellow]8/16/24%[fg] chance to release a homing barrage on enemy kill',
  ['critical_strike'] = '[yellow]5/10/15%[fg] chance for attacks to critically strike, dealing [yellow]2x[fg] damage',
  ['noxious_strike'] = '[yellow]8/16/24%[fg] chance for attacks to poison, dealing [yellow]20%[fg] dps for [yellow]3[fg] seconds',
  ['infesting_strike'] = '[yellow]10/20/30%[fg] chance for attacks to spawn [yellow]2[fg] critters on kill',
  ['kinetic_strike'] = '[yellow]10/20/30%[fg] chance for attacks to push enemies away with high force',
  ['burning_strike'] = '[yellow]15%[fg] chance for attacks to burn, dealing [yellow]20%[fg] dps for [yellow]3[fg] seconds',
  ['lucky_strike'] = '[yellow]8%[fg] chance for attacks to cause enemies to drop gold on death',
  ['healing_strike'] = '[yellow]8%[fg] chance for attacks to spawn a healing orb on kill',
  ['stunning_strike'] = '[yellow]8/16/24%[fg] chance for attacks to stun for [yellow]2[fg] seconds',
  ['silencing_strike'] = '[yellow]8/16/24%[fg] chance for attacks to silence for [yellow]2[fg] seconds on hit',
  ['warping_shots'] = 'projectiles ignore wall collisions and warp around the screen [yellow]1/2/3[fg] times',
  ['culling_strike'] = '[fg]instantly kill elites below [yellow]10/20/30%[fg] max HP',
  ['lightning_strike'] = '[yellow]5/10/15%[fg] chance for projectiles to create chain lightning, dealing [yellow]60/80/100%[fg] damage',
  ['psycholeak'] = '[fg]position [yellow]1[fg] generates [yellow]1[fg] psyker orb every [yellow]10[fg] seconds',
  ['divine_blessing'] = '[fg]generate [yellow]1[fg] healing orb every [yellow]8[fg] seconds',
  ['hardening'] = '[yellow]+150%[fg] defense to all allies for [yellow]3[fg] seconds after an ally dies',
}

-- 被动技能等级描述函数辅助工具
local ylb1 = function(lvl)
  if lvl == 3 then return 'light_bg'
  elseif lvl == 2 then return 'light_bg'
  elseif lvl == 1 then return 'yellow'
  else return 'light_bg' end
end
local ylb2 = function(lvl)
  if lvl == 3 then return 'light_bg'
  elseif lvl == 2 then return 'yellow'
  else return 'light_bg' end
end
local ylb3 = function(lvl)
  if lvl == 3 then return 'yellow'
  else return 'light_bg' end
end
local ts = function(lvl, a, b, c) return '[' .. ylb1(lvl) .. ']' .. tostring(a) .. '[light_bg]/[' .. ylb2(lvl) .. ']' .. tostring(b) .. '[light_bg]/[' .. ylb3(lvl) .. ']' .. tostring(c) .. '[fg]' end

-- 被动技能等级描述定义（动态版本）
passive_descriptions_level = {
  ['centipede'] = function(lvl) return ts(lvl, '+10%', '20%', '30%') .. ' movement speed' end,
  ['ouroboros_technique_r'] = function(lvl) return '[fg]rotating around yourself to the right releases ' .. ts(lvl, '2', '3', '4') .. ' projectiles per second' end,
  ['ouroboros_technique_l'] = function(lvl) return '[fg]rotating around yourself to the left grants ' .. ts(lvl, '+15%', '25%', '35%') .. ' defense to all units' end,
  ['amplify'] = function(lvl) return ts(lvl, '+20%', '35%', '50%') .. ' AoE damage' end,
  ['resonance'] = function(lvl) return '[fg]all AoE attacks deal ' .. ts(lvl, '+3%', '5%', '7%') .. ' damage per unit hit' end,
  ['ballista'] = function(lvl) return ts(lvl, '+20%', '35%', '50%') .. ' projectile damage' end,
  ['call_of_the_void'] = function(lvl) return ts(lvl, '+30%', '60%', '90%') .. ' DoT damage' end,
  ['crucio'] = function(lvl) return '[fg]taking damage also shares that across all enemies at ' .. ts(lvl, '20%', '30%', '40%') .. ' its value' end,
  ['speed_3'] = function(lvl) return '[fg]position [yellow]3[fg] has [yellow]+50%[fg] attack speed' end,
  ['damage_4'] = function(lvl) return '[fg]position [yellow]4[fg] has [yellow]+30%[fg] damage' end,
  ['shoot_5'] = function(lvl) return '[fg]position [yellow]5[fg] shoots [yellow]3[fg] projectiles per second' end,
  ['death_6'] = function(lvl) return '[fg]position [yellow]6[fg] takes [yellow]10%[fg] of its health as damage every [yellow]3[fg] seconds' end,
  ['lasting_7'] = function(lvl) return '[fg]position [yellow]7[fg] will stay alive for [yellow]10[fg] seconds after dying' end,
  ['defensive_stance'] = function(lvl) return '[fg]first and last positions have ' .. ts(lvl, '+10%', '20%', '30%') .. ' defense' end,
  ['offensive_stance'] = function(lvl) return '[fg]first and last positions have ' .. ts(lvl, '+10%', '20%', '30%') .. ' damage' end,
  ['kinetic_bomb'] = function(lvl) return '[fg]when an ally dies it explodes, launching enemies away' end,
  ['porcupine_technique'] = function(lvl) return '[fg]when an ally dies it explodes, releasing piercing projectiles' end,
  ['last_stand'] = function(lvl) return '[fg]the last unit alive is fully healed and receives a [yellow]+20%[fg] bonus to all stats' end,
  ['seeping'] = function(lvl) return '[fg]enemies taking DoT damage have ' .. ts(lvl, '-15%', '25%', '35%') .. ' defense' end,
  ['deceleration'] = function(lvl) return '[fg]enemies taking DoT damage have ' .. ts(lvl, '-15%', '25%', '35%') .. ' movement speed' end,
  ['annihilation'] = function(lvl) return '[fg]when a voider dies deal its DoT damage to all enemies for [yellow]3[fg] seconds' end,
  ['malediction'] = function(lvl) return ts(lvl, '+1', '3', '5') .. ' max curse targets to all allied cursers' end,
  ['hextouch'] = function(lvl) return '[fg]enemies take ' .. ts(lvl, '10', '15', '20') .. 'damage per second for [yellow]3[fg] seconds when cursed' end,
  ['whispers_of_doom'] = function(lvl) return '[fg]curses apply doom, deal ' .. ts(lvl, '100', '150', '200') .. ' every ' .. ts(lvl, '4', '3', '2') .. ' doom instances' end,
  ['tremor'] = function(lvl) return '[fg]when enemies hit walls they create an area based to the knockback force' end,
  ['heavy_impact'] = function(lvl) return '[fg]when enemies hit walls they take damage based on the knockback force' end,
  ['fracture'] = function(lvl) return '[fg]when enemies hit walls they explode into projectiles' end,
  ['meat_shield'] = function(lvl) return '[fg]critters [yellow]block[fg] enemy projectiles' end,
  ['hive'] = function(lvl) return '[fg]critters have ' .. ts(lvl, '+1', '2', '3') .. ' HP' end,
  ['baneling_burst'] = function(lvl) return '[fg]critters die immediately on contact but also deal ' .. ts(lvl, '50', '100', '150') .. ' AoE damage' end,
  ['blunt_arrow'] = function(lvl) return '[fg]ranger arrows have ' .. ts(lvl, '+10%', '20%', '30%') .. ' chance to knockback' end,
  ['explosive_arrow'] = function(lvl) return '[fg]ranger arrows have ' .. ts(lvl, '+10%', '20%', '30%') .. ' chance to deal ' .. ts(lvl, '10%', '20%', '30%') .. ' AoE damage' end,
  ['divine_machine_arrow'] = function(lvl) return '[fg]ranger arrows have a ' .. ts(lvl, '10%', '20%', '30%') .. ' chance to seek and pierce ' .. ts(lvl, '1', '2', '3') .. ' times' end,
  ['chronomancy'] = function(lvl) return '[fg]mages cast their spells ' .. ts(lvl, '15%', '25%', '35%') .. ' faster' end,
  ['awakening'] = function(lvl) return ts(lvl, '+50%', '75%', '100%') .. ' attack speed and damage to [yellow]1[fg] mage every round for that round' end,
  ['divine_punishment'] = function(lvl) return '[fg]deal damage to all enemies based on how many mages you have' end,
  ['assassination'] = function(lvl) return '[fg]crits from rogues deal ' .. ts(lvl, '8x', '10x', '12x') .. ' damage but normal attacks deal [yellow]half[fg] damage' end,
  ['flying_daggers'] = function(lvl) return '[fg]all projectiles thrown by rogues chain ' .. ts(lvl, '+2', '3', '4') .. ' times' end,
  ['ultimatum'] = function(lvl) return '[fg]projectiles that chain gain ' .. ts(lvl, '+10%', '20%', '30%') .. ' damage with each chain' end,
  ['magnify'] = function(lvl) return ts(lvl, '+20%', '35%', '50%') .. ' area size' end,
  ['echo_barrage'] = function(lvl) return ts(lvl, '10%', '20%', '30%') .. ' chance to create ' .. ts(lvl, '1', '2', '3') .. ' secondary AoEs on AoE hit' end,
  ['unleash'] = function(lvl) return '[fg]all nukers gain [yellow]+1%[fg] area size and damage every second' end,
  ['reinforce'] = function(lvl) return ts(lvl, '+10%', '20%', '30%') .. ' global damage, defense and aspd if you have one or more enchanters' end,
  ['payback'] = function(lvl) return ts(lvl, '+2%', '5%', '8%') .. ' damage to all allies whenever an enchanter is hit' end,
  ['enchanted'] = function(lvl) return ts(lvl, '+33%', '66%', '99%') .. ' attack speed to a random unit if you have two or more enchanters' end,
  ['freezing_field'] = function(lvl) return '[fg]creates an area that slows enemies by [yellow]50%[fg] for [yellow]2[fg] seconds on sorcerer spell repeat' end,
  ['burning_field'] = function(lvl) return '[fg]creates an area that deals [yellow]30[fg] dps for [yellow]2[fg] seconds on sorcerer spell repeat' end,
  ['gravity_field'] = function(lvl) return '[fg]creates an area that pulls enemies in for [yellow]1[fg] seconds on sorcerer spell repeat' end,
  ['magnetism'] = function(lvl) return '[fg]gold coins and healing orbs are attracted to the snake from a longer range' end,
  ['insurance'] = function(lvl) return "[fg]heroes have [yellow]4[fg] times the chance of mercenary's bonus to drop [yellow]2[fg] gold on death" end,
  ['dividends'] = function(lvl) return '[fg]mercenaries deal [yellow]+X%[fg] damage, where X is how much gold you have' end,
  ['berserking'] = function(lvl) return '[fg]all warriors have up to ' .. ts(lvl, '+50%', '75%', '100%') .. ' attack speed based on missing HP' end,
  ['unwavering_stance'] = function(lvl) return '[fg]all warriors gain ' .. ts(lvl, '+4%', '8%', '12%') .. ' defense every [yellow]5[fg] seconds' end,
  ['unrelenting_stance'] = function(lvl) return ts(lvl, '+2%', '5%', '8%') .. ' defense to all allies whenever a warrior is hit' end,
  ['blessing'] = function(lvl) return ts(lvl, '+10%', '20%', '30%') .. ' healing effectiveness' end,
  ['haste'] = function(lvl) return '[yellow]+50%[fg] movement speed that decays over [yellow]4[fg] seconds on healing orb pick up' end,
  ['divine_barrage'] = function(lvl) return ts(lvl, '20%', '40%', '60%') .. ' chance to release a ricocheting barrage on healing orb pick up' end,
  ['orbitism'] = function(lvl) return ts(lvl, '+25%', '50%', '75%') .. ' psyker orb movement speed' end,
  ['psyker_orbs'] = function(lvl) return ts(lvl, '+1', '2', '4') .. ' psyker orbs' end,
  ['psychosense'] = function(lvl) return ts(lvl, '+33%', '66%', '99%') .. ' orb range' end,
  ['psychosink'] = function(lvl) return '[fg]psyker orbs deal ' .. ts(lvl, '+40%', '80%', '120%') .. ' damage' end,
  ['rearm'] = function(lvl) return '[fg]constructs repeat their attacks once' end,
  ['taunt'] = function(lvl) return ts(lvl, '10%', '20%', '30%') .. ' chance for constructs to taunt nearby enemies on attack' end,
  ['construct_instability'] = function(lvl) return '[fg]constructs explode when disappearing, dealing ' .. ts(lvl, '100', '150', '200%') .. ' damage' end,
  ['intimidation'] = function(lvl) return '[fg]enemies spawn with ' .. ts(lvl, '-10', '20', '30%') .. ' max HP' end,
  ['vulnerability'] = function(lvl) return '[fg]enemies take ' .. ts(lvl, '+10', '20', '30%').. ' damage' end,
  ['temporal_chains'] = function(lvl) return '[fg]enemies are ' .. ts(lvl, '10', '20', '30%').. ' slower' end,
  ['ceremonial_dagger'] = function(lvl) return '[fg]killing an enemy fires a homing dagger' end,
  ['homing_barrage'] = function(lvl) return ts(lvl, '8', '16', '24%') .. ' chance to release a homing barrage on enemy kill' end,
  ['critical_strike'] = function(lvl) return ts(lvl, '5', '10', '15%') .. ' chance for attacks to critically strike, dealing [yellow]2x[fg] damage' end,
  ['noxious_strike'] = function(lvl) return ts(lvl, '8', '16', '24%') .. ' chance for attacks to poison, dealing [yellow]20%[fg] dps for [yellow]3[fg] seconds' end,
  ['infesting_strike'] = function(lvl) return ts(lvl, '10', '20', '30%') .. ' chance for attacks to spawn [yellow]2[fg] critters on kill' end,
  ['kinetic_strike'] = function(lvl) return ts(lvl, '10', '20', '30%') .. ' chance for attacks to push enemies away with high force' end,
  ['burning_strike'] = function(lvl) return '[yellow]15%[fg] chance for attacks to burn, dealing [yellow]20%[fg] dps for [yellow]3[fg] seconds' end,
  ['lucky_strike'] = function(lvl) return '[yellow]8%[fg] chance for attacks to cause enemies to drop gold on death' end,
  ['healing_strike'] = function(lvl) return '[yellow]8%[fg] chance for attacks to spawn a healing orb on kill' end,
  ['stunning_strike'] = function(lvl) return ts(lvl, '8', '16', '24%') .. ' chance for attacks to stun for [yellow]2[fg] seconds' end,
  ['silencing_strike'] = function(lvl) return ts(lvl, '8', '16', '24%') .. ' chance for attacks to silence for [yellow]2[fg] seconds on hit' end,
  ['warping_shots'] = function(lvl) return 'projectiles ignore wall collisions and warp around the screen ' .. ts(lvl, '1', '2', '3') .. ' times' end,
  ['culling_strike'] = function(lvl) return '[fg]instantly kill elites below ' .. ts(lvl, '10', '20', '30%') .. ' max HP' end,
  ['lightning_strike'] = function(lvl) return ts(lvl, '5', '10', '15%') .. ' chance for projectiles to create chain lightning, dealing ' .. ts(lvl, '60', '80', '100%') .. ' damage' end,
  ['psycholeak'] = function(lvl) return '[fg]position [yellow]1[fg] generates [yellow]1[fg] psyker orb every [yellow]10[fg] seconds' end,
  ['divine_blessing'] = function(lvl) return '[fg]generate [yellow]1[fg] healing orb every [yellow]8[fg] seconds' end,
  ['hardening'] = function(lvl) return '[yellow]+150%[fg] defense to all allies for [yellow]3[fg] seconds after an ally dies' end,
}

-- 不可升级道具列表
unlevellable_items = {
  'speed_3', 'damage_4', 'shoot_5', 'death_6', 'lasting_7', 'kinetic_bomb', 'porcupine_technique', 'last_stand', 'annihilation',
  'tremor', 'heavy_impact', 'fracture', 'meat_shield', 'divine_punishment', 'unleash', 'freezing_field', 'burning_field', 'gravity_field',
  'magnetism', 'insurance', 'dividends', 'haste', 'rearm', 'ceremonial_dagger', 'burning_strike', 'lucky_strike', 'healing_strike', 'psycholeak', 'divine_blessing', 'hardening',
}