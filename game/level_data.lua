--[[
模块：level_data.lua（关卡数据管理）
职责：
- 定义关卡到层级权重的映射关系
- 管理关卡金币获得数值
- 定义精英敌人生成权重和类型
- 管理Boss出现规律和商店概率
- 提供关卡平衡相关的数据和函数
依赖：无（纯数据模块）
]]--

-- 关卡到层级权重映射（用于角色生成概率）
level_to_tier_weights = {
  [1] = {90, 10, 0, 0},
  [2] = {80, 15, 5, 0},
  [3] = {75, 20, 5, 0},
  [4] = {70, 20, 10, 0},
  [5] = {70, 20, 10, 0},
  [6] = {65, 25, 10, 0},
  [7] = {60, 25, 15, 0},
  [8] = {55, 25, 15, 5},
  [9] = {50, 30, 15, 5},
  [10] = {50, 30, 15, 5},
  [11] = {45, 30, 20, 5},
  [12] = {40, 30, 20, 10},
  [13] = {35, 30, 25, 10},
  [14] = {30, 30, 25, 15},
  [15] = {25, 30, 30, 15},
  [16] = {25, 25, 30, 20},
  [17] = {20, 25, 35, 20},
  [18] = {15, 25, 35, 25},
  [19] = {10, 25, 40, 25},
  [20] = {5, 25, 40, 30},
  [21] = {0, 25, 40, 35},
  [22] = {0, 20, 40, 40},
  [23] = {0, 20, 35, 45},
  [24] = {0, 10, 30, 60},
  [25] = {0, 0, 0, 100},
}

-- 关卡到金币获得映射（最小值，最大值）
level_to_gold_gained = {
  [1] = {3, 3},
  [2] = {3, 3},
  [3] = {5, 6},
  [4] = {4, 5},
  [5] = {5, 8},
  [6] = {8, 10},
  [7] = {8, 10},
  [8] = {12, 14},
  [9] = {14, 18},
  [10] = {10, 13},
  [11] = {12, 15},
  [12] = {18, 20},
  [13] = {10, 14},
  [14] = {12, 16},
  [15] = {14, 18},
  [16] = {12, 12},
  [17] = {12, 12},
  [18] = {20, 24},
  [19] = {8, 12},
  [20] = {10, 14},
  [21] = {20, 28},
  [22] = {32, 32},
  [23] = {36, 36},
  [24] = {48, 48},
  [25] = {100, 100},
}

-- 生成后续关卡金币获得数据（26-5000关）
local k = 1
for i = 26, 5000 do
  local n = i % 25
  if n == 0 then
    n = 25
    k = k + 1
  end
  level_to_gold_gained[i] = {level_to_gold_gained[n][1]*k, level_to_gold_gained[n][2]*k}
end

-- 关卡到精英敌人生成权重映射
level_to_elite_spawn_weights = {
  [1] = {0},
  [2] = {4, 2},
  [3] = {10},
  [4] = {4, 4},
  [5] = {4, 3, 2},
  [6] = {12},
  [7] = {5, 3, 2},
  [8] = {6, 3, 3, 3},
  [9] = {14},
  [10] = {8, 4},
  [11] = {8, 6, 2},
  [12] = {16},
  [13] = {8, 8},
  [14] = {12, 6},
  [15] = {18},
  [16] = {10, 6, 4},
  [17] = {6, 5, 4, 3},
  [18] = {18},
  [19] = {10, 6},
  [20] = {8, 6, 2},
  [21] = {22},
  [22] = {10, 8, 4},
  [23] = {20, 5, 5},
  [24] = {30},
  [25] = {5, 5, 5, 5, 5, 5},
}

-- 生成后续关卡精英敌人生成权重（26-5000关）
local k = 1
local l = 0.2
for i = 26, 5000 do
  local n = i % 25
  if n == 0 then
    n = 25
    k = k + 1
    l = l*2
  end
  local a, b, c, d, e, f = unpack(level_to_elite_spawn_weights[n])
  a = (a or 0) + (a or 0)*l
  b = (b or 0) + (b or 0)*l
  c = (c or 0) + (c or 0)*l
  d = (d or 0) + (d or 0)*l
  e = (e or 0) + (e or 0)*l
  f = (f or 0) + (f or 0)*l
  level_to_elite_spawn_weights[i] = {a, b, c, d, e, f}
end

-- 关卡到Boss映射
level_to_boss = {
  [6] = 'speed_booster',
  [12] = 'exploder',
  [18] = 'swarmer',
  [24] = 'forcer',
  [25] = 'randomizer',
}

-- 生成后续关卡Boss映射（26-5000关）
local bosses = {'speed_booster', 'exploder', 'swarmer', 'forcer', 'randomizer'}
level_to_boss[31] = 'speed_booster'
level_to_boss[37] = 'exploder'
level_to_boss[43] = 'swarmer'
level_to_boss[49] = 'forcer'
level_to_boss[50] = 'randomizer'
local i = 31
local k = 1
while i < 5000 do
  level_to_boss[i] = bosses[k]
  k = k + 1
  if k == 5 then i = i + 1 else i = i + 6 end
  if k == 6 then k = 1 end
end

-- 关卡到精英敌人类型映射
level_to_elite_spawn_types = {
  [1] = {'speed_booster'},
  [2] = {'speed_booster', 'shooter'},
  [3] = {'speed_booster'},
  [4] = {'speed_booster', 'exploder'},
  [5] = {'speed_booster', 'exploder', 'shooter'},
  [6] = {'speed_booster'},
  [7] = {'speed_booster', 'exploder', 'headbutter'},
  [8] = {'speed_booster', 'exploder', 'headbutter', 'shooter'},
  [9] = {'shooter'},
  [10] = {'exploder', 'headbutter'},
  [11] = {'exploder', 'headbutter', 'tank'},
  [12] = {'exploder'},
  [13] = {'speed_booster', 'shooter'},
  [14] = {'speed_booster', 'spawner'},
  [15] = {'shooter'},
  [16] = {'speed_booster', 'exploder', 'spawner'},
  [17] = {'speed_booster', 'exploder', 'spawner', 'shooter'},
  [18] = {'spawner'},
  [19] = {'headbutter', 'tank'},
  [20] = {'speed_booster', 'tank', 'spawner'},
  [21] = {'headbutter'},
  [22] = {'speed_booster', 'headbutter', 'tank'},
  [23] = {'headbutter', 'tank', 'shooter'},
  [24] = {'tank'},
  [25] = {'speed_booster', 'exploder', 'headbutter', 'tank', 'shooter', 'spawner'},
}

-- 生成后续关卡精英敌人类型映射（26-5000关）
for i = 26, 5000 do
  local n = i % 25
  if n == 0 then
    n = 25
  end
  level_to_elite_spawn_types[i] = level_to_elite_spawn_types[n]
end

-- 关卡到商店概率映射
level_to_shop_odds = {
  [1] = {70, 20, 10, 0},
  [2] = {50, 30, 15, 5},
  [3] = {25, 45, 20, 10},
  [4] = {10, 25, 45, 20},
  [5] = {5, 15, 30, 50},
}

-- 功能：获取商店特定层级出现的概率
-- 参数：lvl(number) - 商店等级, tier(number) - 角色层级
-- 返回：number - 出现概率百分比
get_shop_odds = function(lvl, tier)
  if lvl == 1 then
    if tier == 1 then
      return 70
    elseif tier == 2 then
      return 20
    elseif tier == 3 then
      return 10
    elseif tier == 4 then
      return 0
    end
  elseif lvl == 2 then
    if tier == 1 then
      return 50
    elseif tier == 2 then
      return 30
    elseif tier == 3 then
      return 15
    elseif tier == 4 then
      return 5
    end
  elseif lvl == 3 then
    if tier == 1 then
      return 25
    elseif tier == 2 then
      return 45
    elseif tier == 3 then
      return 20
    elseif tier == 4 then
      return 10
    end
  elseif lvl == 4 then
    if tier == 1 then
      return 10
    elseif tier == 2 then
      return 25
    elseif tier == 3 then
      return 45
    elseif tier == 4 then
      return 20
    end
  elseif lvl == 5 then
    if tier == 1 then
      return 5
    elseif tier == 2 then
      return 15
    elseif tier == 3 then
      return 30
    elseif tier == 4 then
      return 50
    end
  end
end