--[[
模块：character_data.lua（角色数据管理）
职责：
- 定义所有角色相关的静态数据（名称、颜色、职业等）
- 提供角色属性和描述信息
- 管理角色等级和层级关系
- 提供角色相关的辅助函数
依赖：shared.lua（颜色定义）
]]--

-- 角色名称定义
character_names = {
  ['vagrant'] = 'Vagrant',
  ['swordsman'] = 'Swordsman',
  ['wizard'] = 'Wizard',
  ['magician'] = 'Magician',
  ['archer'] = 'Archer',
  ['scout'] = 'Scout',
  ['cleric'] = 'Cleric',
  ['outlaw'] = 'Outlaw',
  ['blade'] = 'Blade',
  ['elementor'] = 'Elementor',
  ['saboteur'] = 'Saboteur',
  ['bomber'] = 'Bomber',
  ['stormweaver'] = 'Stormweaver',
  ['sage'] = 'Sage',
  ['squire'] = 'Squire',
  ['cannoneer'] = 'Cannoneer',
  ['dual_gunner'] = 'Dual Gunner',
  ['hunter'] = 'Hunter',
  ['sentry'] = 'Sentry',
  ['chronomancer'] = 'Chronomancer',
  ['spellblade'] = 'Spellblade',
  ['psykeeper'] = 'Psykeeper',
  ['engineer'] = 'Engineer',
  ['plague_doctor'] = 'Plague Doctor',
  ['barbarian'] = 'Barbarian',
  ['juggernaut'] = 'Juggernaut',
  ['lich'] = 'Lich',
  ['cryomancer'] = 'Cryomancer',
  ['pyromancer'] = 'Pyromancer',
  ['corruptor'] = 'Corruptor',
  ['beastmaster'] = 'Beastmaster',
  ['launcher'] = 'Launcher',
  ['jester'] = 'Jester',
  ['assassin'] = 'Assassin',
  ['host'] = 'Host',
  ['carver'] = 'Carver',
  ['bane'] = 'Bane',
  ['psykino'] = 'Psykino',
  ['barrager'] = 'Barrager',
  ['highlander'] = 'Highlander',
  ['fairy'] = 'Fairy',
  ['priest'] = 'Priest',
  ['infestor'] = 'Infestor',
  ['flagellant'] = 'Flagellant',
  ['arcanist'] = 'Arcanist',
  ['illusionist'] = 'Illusionist',
  ['artificer'] = 'Artificer',
  ['witch'] = 'Witch',
  ['silencer'] = 'Silencer',
  ['vulcanist'] = 'Vulcanist',
  ['warden'] = 'Warden',
  ['psychic'] = 'Psychic',
  ['miner'] = 'Miner',
  ['merchant'] = 'Merchant',
  ['usurer'] = 'Usurer',
  ['gambler'] = 'Gambler',
  ['thief'] = 'Thief',
}

-- 角色颜色定义（延迟初始化以避免颜色变量未定义的问题）
character_colors = {}

-- 功能：初始化角色颜色数据
-- 参数：无
-- 返回：无
-- 说明：必须在shared_init()之后调用，确保颜色变量已定义
function init_character_colors()
  character_colors = {
    ['vagrant'] = fg[0],
    ['swordsman'] = yellow[0],
    ['wizard'] = blue[0],
    ['magician'] = blue[0],
    ['archer'] = green[0],
    ['scout'] = red[0],
    ['cleric'] = green[0],
    ['outlaw'] = red[0],
    ['blade'] = yellow[0],
    ['elementor'] = blue[0],
    ['saboteur'] = orange[0],
    ['bomber'] = orange[0],
    ['stormweaver'] = blue[0],
    ['sage'] = purple[0],
    ['squire'] = yellow[0],
    ['cannoneer'] = orange[0],
    ['dual_gunner'] = green[0],
    ['hunter'] = green[0],
    ['sentry'] = green[0],
    ['chronomancer'] = blue[0],
    ['spellblade'] = blue[0],
    ['psykeeper'] = fg[0],
    ['engineer'] = orange[0],
    ['plague_doctor'] = purple[0],
    ['barbarian'] = yellow[0],
    ['juggernaut'] = yellow[0],
    ['lich'] = blue[0],
    ['cryomancer'] = blue[0],
    ['pyromancer'] = red[0],
    ['corruptor'] = orange[0],
    ['beastmaster'] = red[0],
    ['launcher'] = yellow[0],
    ['jester'] = red[0],
    ['assassin'] = purple[0],
    ['host'] = orange[0],
    ['carver'] = green[0],
    ['bane'] = purple[0],
    ['psykino'] = fg[0],
    ['barrager'] = green[0],
    ['highlander'] = yellow[0],
    ['fairy'] = green[0],
    ['priest'] = green[0],
    ['infestor'] = orange[0],
    ['flagellant'] = fg[0],
    ['arcanist'] = blue2[0],
    ['illusionist'] = blue2[0],
    ['artificer'] = blue2[0],
    ['witch'] = purple[0],
    ['silencer'] = blue2[0],
    ['vulcanist'] = red[0],
    ['warden'] = yellow[0],
    ['psychic'] = fg[0],
    ['miner'] = yellow2[0],
    ['merchant'] = yellow2[0],
    ['usurer'] = purple[0],
    ['gambler'] = yellow2[0],
    ['thief'] = red[0],
  }
end

-- 角色颜色字符串定义
character_color_strings = {
  ['vagrant'] = 'fg',
  ['swordsman'] = 'yellow',
  ['wizard'] = 'blue',
  ['magician'] = 'blue',
  ['archer'] = 'green',
  ['scout'] = 'red',
  ['cleric'] = 'green',
  ['outlaw'] = 'red',
  ['blade'] = 'yellow',
  ['elementor'] = 'blue',
  ['saboteur'] = 'orange',
  ['bomber'] = 'orange',
  ['stormweaver'] = 'blue',
  ['sage'] = 'purple',
  ['squire'] = 'yellow',
  ['cannoneer'] = 'orange',
  ['dual_gunner'] = 'green',
  ['hunter'] = 'green',
  ['sentry'] = 'green',
  ['chronomancer'] = 'blue',
  ['spellblade'] = 'blue',
  ['psykeeper'] = 'fg',
  ['engineer'] = 'orange',
  ['plague_doctor'] = 'purple',
  ['barbarian'] = 'yellow',
  ['juggernaut'] = 'yellow',
  ['lich'] = 'blue',
  ['cryomancer'] = 'blue',
  ['pyromancer'] = 'red',
  ['corruptor'] = 'orange',
  ['beastmaster'] = 'red',
  ['launcher'] = 'yellow',
  ['jester'] = 'red',
  ['assassin'] = 'purple',
  ['host'] = 'orange',
  ['carver'] = 'green',
  ['bane'] = 'purple',
  ['psykino'] = 'fg',
  ['barrager'] = 'green',
  ['highlander'] = 'yellow',
  ['fairy'] = 'green',
  ['priest'] = 'green',
  ['infestor'] = 'orange',
  ['flagellant'] = 'fg',
  ['arcanist'] = 'blue2',
  ['illusionist'] = 'blue2',
  ['artificer'] = 'blue2',
  ['witch'] = 'purple',
  ['silencer'] = 'blue2',
  ['vulcanist'] = 'red',
  ['warden'] = 'yellow',
  ['psychic'] = 'fg',
  ['miner'] = 'yellow2',
  ['merchant'] = 'yellow2',
  ['usurer'] = 'purple',
  ['gambler'] = 'yellow2',
  ['thief'] = 'red',
}

-- 角色职业定义
character_classes = {
  ['vagrant'] = {'explorer', 'psyker'},
  ['swordsman'] = {'warrior'},
  ['wizard'] = {'mage', 'nuker'},
  ['magician'] = {'mage'},
  ['archer'] = {'ranger'},
  ['scout'] = {'rogue'},
  ['cleric'] = {'healer'},
  ['outlaw'] = {'warrior', 'rogue'},
  ['blade'] = {'warrior', 'nuker'},
  ['elementor'] = {'mage', 'nuker'},
  -- ['saboteur'] = {'rogue', 'conjurer', 'nuker'},
  ['bomber'] = {'nuker', 'conjurer'},
  ['stormweaver'] = {'enchanter'},
  ['sage'] = {'nuker', 'forcer'},
  ['squire'] = {'warrior', 'enchanter'},
  ['cannoneer'] = {'ranger', 'nuker'},
  ['dual_gunner'] = {'ranger', 'rogue'},
  -- ['hunter'] = {'ranger', 'conjurer', 'forcer'},
  ['sentry'] = {'ranger', 'conjurer'},
  ['chronomancer'] = {'mage', 'enchanter'},
  ['spellblade'] = {'mage', 'rogue'},
  ['psykeeper'] = {'healer', 'psyker'},
  ['engineer'] = {'conjurer'},
  ['plague_doctor'] = {'nuker', 'voider'},
  ['barbarian'] = {'curser', 'warrior'},
  ['juggernaut'] = {'forcer', 'warrior'},
  ['lich'] = {'mage'},
  ['cryomancer'] = {'mage', 'voider'},
  ['pyromancer'] = {'mage', 'nuker', 'voider'},
  ['corruptor'] = {'ranger', 'swarmer'},
  ['beastmaster'] = {'rogue', 'swarmer'},
  ['launcher'] = {'curser', 'forcer'},
  ['jester'] = {'curser', 'rogue'},
  ['assassin'] = {'rogue', 'voider'},
  ['host'] = {'swarmer'},
  ['carver'] = {'conjurer', 'healer'},
  ['bane'] = {'curser', 'voider'},
  ['psykino'] = {'mage', 'psyker', 'forcer'},
  ['barrager'] = {'ranger', 'forcer'},
  ['highlander'] = {'warrior'},
  ['fairy'] = {'enchanter', 'healer'},
  ['priest'] = {'healer'},
  ['infestor'] = {'curser', 'swarmer'},
  ['flagellant'] = {'psyker', 'enchanter'},
  ['arcanist'] = {'sorcerer'},
  -- ['illusionist'] = {'sorcerer', 'conjurer'},
  ['artificer'] = {'sorcerer', 'conjurer'},
  ['witch'] = {'sorcerer', 'voider'},
  ['silencer'] = {'sorcerer', 'curser'},
  ['vulcanist'] = {'sorcerer', 'nuker'},
  ['warden'] = {'sorcerer', 'forcer'},
  ['psychic'] = {'sorcerer', 'psyker'},
  ['miner'] = {'mercenary'},
  ['merchant'] = {'mercenary'},
  ['usurer'] = {'curser', 'mercenary', 'voider'},
  ['gambler'] = {'mercenary', 'sorcerer'},
  ['thief'] = {'rogue', 'mercenary'},
}

-- 角色职业字符串定义
character_class_strings = {
  ['vagrant'] = '[fg]Explorer, Psyker',
  ['swordsman'] = '[yellow]Warrior',
  ['wizard'] = '[blue]Mage, [red]Nuker',
  ['magician'] = '[blue]Mage',
  ['archer'] = '[green]Ranger',
  ['scout'] = '[red]Rogue',
  ['cleric'] = '[green]Healer',
  ['outlaw'] = '[yellow]Warrior, [red]Rogue',
  ['blade'] = '[yellow]Warrior, [red]Nuker',
  ['elementor'] = '[blue]Mage, [red]Nuker',
  -- ['saboteur'] = '[red]Rogue, [orange]Conjurer, [red]Nuker',
  ['bomber'] = '[red]Nuker, [orange]Builder',
  ['stormweaver'] = '[blue]Enchanter',
  ['sage'] = '[red]Nuker, [yellow]Forcer',
  ['squire'] = '[yellow]Warrior, [blue]Enchanter',
  ['cannoneer'] = '[green]Ranger, [red]Nuker',
  ['dual_gunner'] = '[green]Ranger, [red]Rogue',
  -- ['hunter'] = '[green]Ranger, [orange]Conjurer, [yellow]Forcer',
  ['sentry'] = '[green]Ranger, [orange]Builder',
  ['chronomancer'] = '[blue]Mage, Enchanter',
  ['spellblade'] = '[blue]Mage, [red]Rogue',
  ['psykeeper'] = '[green]Healer, [fg]Psyker',
  ['engineer'] = '[orange]Builder',
  ['plague_doctor'] = '[red]Nuker, [purple]Voider',
  ['barbarian'] = '[purple]Curser, [yellow]Warrior',
  ['juggernaut'] = '[yellow]Forcer, Warrior',
  ['lich'] = '[blue]Mage',
  ['cryomancer'] = '[blue]Mage, [purple]Voider',
  ['pyromancer'] = '[blue]Mage, [red]Nuker, [purple]Voider',
  ['corruptor'] = '[green]Ranger, [orange]Swarmer',
  ['beastmaster'] = '[red]Rogue, [orange]Swarmer',
  ['launcher'] = '[yellow]Forcer, [purple]Curser',
  ['jester'] = '[purple]Curser, [red]Rogue',
  ['assassin'] = '[red]Rogue, [purple]Voider',
  ['host'] = '[orange]Swarmer',
  ['carver'] = '[orange]Builder, [green]Healer',
  ['bane'] = '[purple]Curser, Voider',
  ['psykino'] = '[blue]Mage, [fg]Psyker, [yellow]Forcer',
  ['barrager'] = '[green]Ranger, [yellow]Forcer',
  ['highlander'] = '[yellow]Warrior',
  ['fairy'] = '[blue]Enchanter, [green]Healer',
  ['priest'] = '[green]Healer',
  ['infestor'] = '[purple]Curser, [orange]Swarmer',
  ['flagellant'] = '[fg]Psyker, [blue]Enchanter',
  ['arcanist'] = '[blue2]Sorcerer',
  -- ['illusionist'] = '[blue2]Sorcerer, [orange]Conjurer',
  ['artificer'] = '[blue2]Sorcerer, [orange]Builder',
  ['witch'] = '[blue2]Sorcerer, [purple]Voider',
  ['silencer'] = '[blue2]Sorcerer, [purple]Curser',
  ['vulcanist'] = '[blue2]Sorcerer, [red]Nuker',
  ['warden'] = '[blue2]Sorcerer, [yellow]Forcer',
  ['psychic'] = '[blue2]Sorcerer, [fg]Psyker',
  ['miner'] = '[yellow2]Mercenary',
  ['merchant'] = '[yellow2]Mercenary',
  ['usurer'] = '[purple]Curser, [yellow2]Mercenary, [purple]Voider',
  ['gambler'] = '[yellow2]Mercenary, [blue2]Sorcerer',
  ['thief'] = '[red]Rogue, [yellow2]Mercenary',
}

-- 功能：获取角色属性字符串，包含血量、攻击力、攻击速度等
-- 参数：character(string) - 角色名称, level(number) - 角色等级
-- 返回：string - 格式化的属性字符串
get_character_stat_string = function(character, level)
  local group = Group():set_as_physics_world(32, 0, 0, {'player', 'enemy', 'projectile', 'enemy_projectile'})
  local player = Player{group = group, leader = true, character = character, level = level, follower_index = 1}
  player:update(0)
  return '[red]HP: [red]' .. player.max_hp .. '[fg], [red]DMG: [red]' .. player.dmg .. '[fg], [green]ASPD: [green]' .. math.round(player.aspd_m, 2) .. 'x[fg], [blue]AREA: [blue]' ..
  math.round(player.area_dmg_m*player.area_size_m, 2) ..  'x[fg], [yellow]DEF: [yellow]' .. math.round(player.def, 2) .. '[fg], [green]MVSPD: [green]' .. math.round(player.v, 2) .. '[fg]'
end

-- 功能：获取角色特定属性值
-- 参数：character(string) - 角色名称, level(number) - 角色等级, stat(string) - 属性名
-- 返回：number - 属性值
get_character_stat = function(character, level, stat)
  local group = Group():set_as_physics_world(32, 0, 0, {'player', 'enemy', 'projectile', 'enemy_projectile'})
  local player = Player{group = group, leader = true, character = character, level = level, follower_index = 1}
  player:update(0)
  return math.round(player[stat], 2)
end

-- 角色技能描述
character_descriptions = {
  ['vagrant'] = function(lvl) return '[fg]shoots a projectile that deals [yellow]' .. get_character_stat('vagrant', lvl, 'dmg') .. '[fg] damage' end,
  ['swordsman'] = function(lvl) return '[fg]deals [yellow]' .. get_character_stat('swordsman', lvl, 'dmg') .. '[fg] damage in an area, deals extra [yellow]' ..
    math.round(get_character_stat('swordsman', lvl, 'dmg')*0.15, 2) .. '[fg] damage per unit hit' end,
  ['wizard'] = function(lvl) return '[fg]shoots a projectile that deals [yellow]' .. get_character_stat('wizard', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['magician'] = function(lvl) return '[fg]creates a small area that deals [yellow]' .. get_character_stat('magician', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['archer'] = function(lvl) return '[fg]shoots an arrow that deals [yellow]' .. get_character_stat('archer', lvl, 'dmg') .. '[fg] damage and pierces' end,
  ['scout'] = function(lvl) return '[fg]throws a knife that deals [yellow]' .. get_character_stat('scout', lvl, 'dmg') .. '[fg] damage and chains [yellow]3[fg] times' end,
  ['cleric'] = function(lvl) return '[fg]creates [yellow]1[fg] healing orb every [yellow]8[fg] seconds' end,
  ['outlaw'] = function(lvl) return '[fg]throws a fan of [yellow]5[fg] knives, each dealing [yellow]' .. get_character_stat('outlaw', lvl, 'dmg') .. '[fg] damage' end,
  ['blade'] = function(lvl) return '[fg]throws multiple blades that deal [yellow]' .. get_character_stat('blade', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['elementor'] = function(lvl) return '[fg]deals [yellow]' .. get_character_stat('elementor', lvl, 'dmg') .. ' AoE[fg] damage in a large area centered on a random target' end,
  ['saboteur'] = function(lvl) return '[fg]calls [yellow]2[fg] saboteurs to seek targets and deal [yellow]' .. get_character_stat('saboteur', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['bomber'] = function(lvl) return '[fg]plants a bomb, when it explodes it deals [yellow]' .. 2*get_character_stat('bomber', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['stormweaver'] = function(lvl) return '[fg]infuses projectiles with chain lightning that deals [yellow]20%[fg] damage to [yellow]2[fg] enemies' end,
  ['sage'] = function(lvl) return '[fg]shoots a slow projectile that draws enemies in' end,
  ['squire'] = function(lvl) return '[yellow]+20%[fg] damage and defense to all allies' end,
  ['cannoneer'] = function(lvl) return '[fg]shoots a projectile that deals [yellow]' .. 2*get_character_stat('cannoneer', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['dual_gunner'] = function(lvl) return '[fg]shoots two parallel projectiles, each dealing [yellow]' .. get_character_stat('dual_gunner', lvl, 'dmg') .. '[fg] damage' end,
  ['hunter'] = function(lvl) return '[fg]shoots an arrow that deals [yellow]' .. get_character_stat('hunter', lvl, 'dmg') .. '[fg] damage and has a [yellow]20%[fg] chance to summon a pet' end,
  ['sentry'] = function(lvl) return '[fg]spawns a rotating turret that shoots [yellow]4[fg] projectiles, each dealing [yellow]' .. get_character_stat('sentry', lvl, 'dmg') .. '[fg] damage' end,
  ['chronomancer'] = function(lvl) return '[yellow]+20%[fg] attack speed to all allies' end,
  ['spellblade'] = function(lvl) return '[fg]throws knives that deal [yellow]' .. get_character_stat('spellblade', lvl, 'dmg') .. '[fg] damage, pierce and spiral outwards' end,
  ['psykeeper'] = function(lvl) return '[fg]creates [yellow]3[fg] healing orbs every time the psykeeper takes [yellow]25%[fg] of its max HP in damage' end,
  ['engineer'] = function(lvl) return '[fg]drops turrets that shoot bursts of projectiles, each dealing [yellow]' .. get_character_stat('engineer', lvl, 'dmg') .. '[fg] damage' end,
  ['plague_doctor'] = function(lvl) return '[fg]creates an area that deals [yellow]' .. get_character_stat('plague_doctor', lvl, 'dmg') .. '[fg] damage per second' end,
  ['barbarian'] = function(lvl) return '[fg]deals [yellow]' .. get_character_stat('barbarian', lvl, 'dmg') .. '[fg] AoE damage and stuns enemies hit for [yellow]4[fg] seconds' end,
  ['juggernaut'] = function(lvl) return '[fg]deals [yellow]' .. get_character_stat('juggernaut', lvl, 'dmg') .. '[fg] AoE damage and pushes enemies away with a strong force' end,
  ['lich'] = function(lvl) return '[fg]launches a slow projectile that jumps [yellow]7[fg] times, dealing [yellow]' ..  2*get_character_stat('lich', lvl, 'dmg') .. '[fg] damage per hit' end,
  ['cryomancer'] = function(lvl) return '[fg]nearby enemies take [yellow]' .. get_character_stat('cryomancer', lvl, 'dmg') .. '[fg] damage per second' end,
  ['pyromancer'] = function(lvl) return '[fg]nearby enemies take [yellow]' .. get_character_stat('pyromancer', lvl, 'dmg') .. '[fg] damage per second' end,
  ['corruptor'] = function(lvl) return '[fg]shoots an arrow that deals [yellow]' .. get_character_stat('corruptor', lvl, 'dmg') .. '[fg] damage, spawn [yellow]3[fg] critters if it kills' end,
  ['beastmaster'] = function(lvl) return '[fg]throws a knife that deals [yellow]' .. get_character_stat('beastmaster', lvl, 'dmg') .. '[fg] damage, spawn [yellow]2[fg] critters if it crits' end,
  ['launcher'] = function(lvl) return '[fg]all nearby enemies are pushed after [yellow]4[fg] seconds, taking [yellow]' .. 2*get_character_stat('launcher', lvl, 'dmg') .. '[fg] damage on wall hit' end,
  ['jester'] = function(lvl) return "[fg]curses [yellow]6[fg] nearby enemies for [yellow]6[fg] seconds, they will explode into [yellow]4[fg] knives on death" end,
  ['assassin'] = function(lvl) return '[fg]throws a piercing knife that deals [yellow]' .. get_character_stat('assassin', lvl, 'dmg') .. '[fg] damage + [yellow]' ..
    get_character_stat('assassin', lvl, 'dmg')/2 .. '[fg] damage per second' end,
  ['host'] = function(lvl) return '[fg]periodically spawn [yellow]1[fg] small critter' end,
  ['carver'] = function(lvl) return '[fg]carves a statue that creates [yellow]1[fg] healing orb every [yellow]6[fg] seconds' end,
  ['bane'] = function(lvl) return '[fg]curses [yellow]6[fg] nearby enemies for [yellow]6[fg] seconds, they will create small void rifts on death' end,
  ['psykino'] = function(lvl) return '[fg]pulls enemies together for [yellow]2[fg] seconds' end,
  ['barrager'] = function(lvl) return '[fg]shoots a barrage of [yellow]3[fg] arrows, each dealing [yellow]' .. get_character_stat('barrager', lvl, 'dmg') .. '[fg] damage and pushing enemies' end,
  ['highlander'] = function(lvl) return '[fg]deals [yellow]' .. 5*get_character_stat('highlander', lvl, 'dmg') .. '[fg] AoE damage' end,
  ['fairy'] = function(lvl) return '[fg]creates [yellow]1[fg] healing orb and grants [yellow]1[fg] unit [yellow]+100%[fg] attack speed for [yellow]6[fg] seconds' end,
  ['priest'] = function(lvl) return '[fg]creates [yellow]3[fg] healing orbs every [yellow]12[fg] seconds' end,
  ['infestor'] = function(lvl) return '[fg]curses [yellow]8[fg] nearby enemies for [yellow]6[fg] seconds, they will release [yellow]2[fg] critters on death' end,
  ['flagellant'] = function(lvl) return '[fg]deals [yellow]' .. 2*get_character_stat('flagellant', lvl, 'dmg') .. '[fg] damage to self and grants [yellow]+4%[fg] damage to all allies per cast' end,
  ['arcanist'] = function(lvl) return '[fg]launches a slow moving orb that launches projectiles, each dealing [yellow]' .. get_character_stat('arcanist', lvl, 'dmg') .. '[fg] damage' end,
  ['illusionist'] = function(lvl) return '[fg]launches a projectile that deals [yellow]' .. get_character_stat('illusionist', lvl, 'dmg') .. '[fg] damage and creates copies that do the same' end,
  ['artificer'] = function(lvl) return '[fg]spawns an automaton that shoots a projectile that deals [yellow]' .. get_character_stat('artificer', lvl, 'dmg') .. '[fg] damage' end,
  ['witch'] = function(lvl) return '[fg]creates an area that ricochets and deals [yellow]' .. get_character_stat('witch', lvl, 'dmg') .. '[fg] damage per second' end,
  ['silencer'] = function(lvl) return '[fg]curses [yellow]5[fg] nearby enemies for [yellow]6[fg] seconds, preventing them from using special attacks' end,
  ['vulcanist'] = function(lvl) return '[fg]creates a volcano that explodes the nearby area [yellow]4[fg] times, dealing [yellow]' .. get_character_stat('vulcanist', lvl, 'dmg') .. ' AoE [fg]damage' end,
  ['warden'] = function(lvl) return '[fg]creates a force field around a random unit that prevents enemies from entering' end,
  ['psychic'] = function(lvl) return '[fg]creates a small area that deals [yellow]' .. get_character_stat('psychic', lvl, 'dmg') .. ' AoE[fg] damage' end,
  ['miner'] = function(lvl) return '[fg]picking up gold releases [yellow]4[fg] homing projectiles that each deal [yellow]' .. get_character_stat('miner', lvl, 'dmg') .. ' [fg]damage' end,
  ['merchant'] = function(lvl) return '[fg]gain [yellow]+1[fg] interest for every [yellow]10[fg] gold, up to a max of [yellow]+10[fg] from the merchant' end,
  ['usurer'] = function(lvl) return '[fg]curses [yellow]3[fg] nearby enemies indefinitely with debt, dealing [yellow]' .. get_character_stat('usurer', lvl, 'dmg') .. '[fg] damage per second' end,
  ['gambler'] = function(lvl) return '[fg]deal [yellow]2X[fg] damage to a single random enemy where X is how much gold you have' end,
  ['thief'] = function(lvl) return '[fg]throws a knife that deals [yellow]' .. 2*get_character_stat('thief', lvl, 'dmg') .. '[fg] damage and chains [yellow]5[fg] times' end,
}

-- 角色效果名称
character_effect_names = {
  ['vagrant'] = '[fg]Experience',
  ['swordsman'] = '[yellow]Cleave',
  ['wizard'] = '[blue]Magic Missile',
  ['magician'] = '[blue]Quick Cast',
  ['archer'] = '[green]Bounce Shot',
  ['scout'] = '[red]Dagger Resonance',
  ['cleric'] = '[green]Mass Heal',
  ['outlaw'] = '[red]Flying Daggers',
  ['blade'] = '[yellow]Blade Resonance',
  ['elementor'] = '[blue]Windfield',
  ['saboteur'] = '[orange]Demoman',
  ['bomber'] = '[orange]Demoman',
  ['stormweaver'] = '[blue]Wide Lightning',
  ['sage'] = '[purple]Dimension Compression',
  ['squire'] = '[yellow]Shiny Gear',
  ['cannoneer'] = '[orange]Cannon Barrage',
  ['dual_gunner'] = '[green]Gun Kata',
  ['hunter'] = '[green]Feral Pack',
  ['sentry'] = '[green]Sentry Barrage',
  ['chronomancer'] = '[blue]Quicken',
  ['spellblade'] = '[blue]Spiralism',
  ['psykeeper'] = '[fg]Crucio',
  ['engineer'] = '[orange]Upgrade!!!',
  ['plague_doctor'] = '[purple]Black Death Steam',
  ['barbarian'] = '[yellow]Seism',
  ['juggernaut'] = '[yellow]Brutal Impact',
  ['lich'] = '[blue]Chain Frost',
  ['cryomancer'] = '[blue]Frostbite',
  ['pyromancer'] = '[red]Ignite',
  ['corruptor'] = '[orange]Corruption',
  ['beastmaster'] = '[red]Call of the Wild',
  ['launcher'] = '[orange]Kineticism',
  ['jester'] = "[red]Pandemonium",
  ['assassin'] = '[purple]Toxic Delivery',
  ['host'] = '[orange]Invasion',
  ['carver'] = '[green]World Tree',
  ['bane'] = '[purple]Nightmare',
  ['psykino'] = '[fg]Magnetic Force',
  ['barrager'] = '[green]Barrage',
  ['highlander'] = '[yellow]Moulinet',
  ['fairy'] = '[green]Whimsy',
  ['priest'] = '[green]Divine Intervention',
  ['infestor'] = '[orange]Infestation',
  ['flagellant'] = '[red]Zealotry',
  ['arcanist'] = '[blue2]Arcane Orb',
  ['illusionist'] = '[blue2]Mirror Image',
  ['artificer'] = '[blue2]Spell Formula Efficiency',
  ['witch'] = '[purple]Death Pool',
  ['silencer'] = '[blue2]Arcane Curse',
  ['vulcanist'] = '[red]Lava Burst',
  ['warden'] = '[yellow]Magnetic Field',
  ['psychic'] = '[fg]Mental Strike',
  ['miner'] = '[yellow2]Golden Bolts',
  ['merchant'] = '[yellow2]Item Shop',
  ['usurer'] = '[purple]Bankruptcy',
  ['gambler'] = '[yellow2]Multicast',
  ['thief'] = '[red]Ultrakill',
}

-- 角色效果名称（灰色版本）
character_effect_names_gray = {
  ['vagrant'] = '[light_bg]Experience',
  ['swordsman'] = '[light_bg]Cleave',
  ['wizard'] = '[light_bg]Magic Missile',
  ['magician'] = '[light_bg]Quick Cast',
  ['archer'] = '[light_bg]Bounce Shot',
  ['scout'] = '[light_bg]Dagger Resonance',
  ['cleric'] = '[light_bg]Mass Heal ',
  ['outlaw'] = '[light_bg]Flying Daggers',
  ['blade'] = '[light_bg]Blade Resonance',
  ['elementor'] = '[light_bg]Windfield',
  ['saboteur'] = '[light_bg]Demoman',
  ['bomber'] = '[light_bg]Demoman',
  ['stormweaver'] = '[light_bg]Wide Lightning',
  ['sage'] = '[light_bg]Dimension Compression',
  ['squire'] = '[light_bg]Shiny Gear',
  ['cannoneer'] = '[light_bg]Cannon Barrage',
  ['dual_gunner'] = '[light_bg]Gun Kata',
  ['hunter'] = '[light_bg]Feral Pack',
  ['sentry'] = '[light_bg]Sentry Barrage',
  ['chronomancer'] = '[light_bg]Quicken',
  ['spellblade'] = '[light_bg]Spiralism',
  ['psykeeper'] = '[light_bg]Crucio',
  ['engineer'] = '[light_bg]Upgrade!!!',
  ['plague_doctor'] = '[light_bg]Black Death Steam',
  ['barbarian'] = '[light_bg]Seism',
  ['juggernaut'] = '[light_bg]Brutal Impact',
  ['lich'] = '[light_bg]Chain Frost',
  ['cryomancer'] = '[light_bg]Frostbite',
  ['pyromancer'] = '[light_bg]Ignite',
  ['corruptor'] = '[light_bg]Corruption',
  ['beastmaster'] = '[light_bg]Call of the Wild',
  ['launcher'] = '[light_bg]Kineticism',
  ['jester'] = "[light_bg]Pandemonium",
  ['assassin'] = '[light_bg]Toxic Delivery',
  ['host'] = '[light_bg]Invasion',
  ['carver'] = '[light_bg]World Tree',
  ['bane'] = '[light_bg]Nightmare',
  ['psykino'] = '[light_bg]Magnetic Force',
  ['barrager'] = '[light_bg]Barrage',
  ['highlander'] = '[light_bg]Moulinet',
  ['fairy'] = '[light_bg]Whimsy',
  ['priest'] = '[light_bg]Divine Intervention',
  ['infestor'] = '[light_bg]Infestation',
  ['flagellant'] = '[light_bg]Zealotry',
  ['arcanist'] = '[light_bg]Arcane Orb',
  ['illusionist'] = '[light_bg]Mirror Image',
  ['artificer'] = '[light_bg]Spell Formula Efficiency',
  ['witch'] = '[light_bg]Death Pool',
  ['silencer'] = '[light_bg]Arcane Curse',
  ['vulcanist'] = '[light_bg]Lava Burst',
  ['warden'] = '[light_bg]Magnetic Field',
  ['psychic'] = '[light_bg]Mental Strike',
  ['miner'] = '[light_bg]Golden Bolts',
  ['merchant'] = '[light_bg]Item Shop',
  ['usurer'] = '[light_bg]Bankruptcy',
  ['gambler'] = '[light_bg]Multicast',
  ['thief'] = '[light_bg]Ultrakill',
}

-- 角色效果描述
character_effect_descriptions = {
  ['vagrant'] = function() return '[yellow]+15%[fg] attack speed and damage per active class' end,
  ['swordsman'] = function() return "[fg]the swordsman's damage is [yellow]doubled" end,
  ['wizard'] = function() return '[fg]the projectile chains [yellow]2[fg] times' end,
  ['magician'] = function() return '[yellow]+50%[[fg] attack speed every [yellow]12[fg] seconds for [yellow]6[fg] seconds' end,
  ['archer'] = function() return '[fg]the arrow ricochets off walls [yellow]3[fg] times' end,
  ['scout'] = function() return '[yellow]+25%[fg] damage per chain and [yellow]+3[fg] chains' end,
  ['cleric'] = function() return '[fg]creates [yellow]4[fg] healing orbs every [yellow]8[fg] seconds' end,
  ['outlaw'] = function() return "[yellow]+50%[fg] outlaw attack speed and his knives seek enemies" end,
  ['blade'] = function() return '[fg]deal additional [yellow]' .. math.round(get_character_stat('blade', 3, 'dmg')/3, 2) .. '[fg] damage per enemy hit' end,
  ['elementor'] = function() return '[fg]slows enemies by [yellow]60%[fg] for [yellow]6[fg] seconds on hit' end,
  ['saboteur'] = function() return '[fg]the explosion has [yellow]50%[fg] chance to crit, increasing in size and dealing [yellow]2x[fg] damage' end,
  ['bomber'] = function() return '[yellow]+100%[fg] bomb area and damage' end,
  ['stormweaver'] = function() return "[fg]chain lightning's trigger area of effect and number of units hit is [yellow]doubled" end,
  ['sage'] = function() return '[fg]when the projectile expires deal [yellow]' .. 3*get_character_stat('sage', 3, 'dmg') .. '[fg] damage to all enemies under its influence' end,
  ['squire'] = function() return '[yellow]+30%[fg] damage, attack speed, movement speed and defense to all allies' end,
  ['cannoneer'] = function() return '[fg]showers the hit area in [yellow]7[fg] additional cannon shots that deal [yellow]' .. get_character_stat('cannoneer', 3, 'dmg')/2 .. '[fg] AoE damage' end,
  ['dual_gunner'] = function() return '[fg]every 5th attack shoot in rapid succession for [yellow]2[fg] seconds' end,
  ['hunter'] = function() return '[fg]summons [yellow]3[fg] pets and the pets ricochet off walls once' end,
  ['sentry'] = function() return '[yellow]+50%[fg] sentry turret attack speed and the projectiles ricochet [yellow]twice[fg]' end,
  ['chronomancer'] = function() return '[fg]enemies take damage over time [yellow]50%[fg] faster' end,
  ['spellblade'] = function() return '[fg]faster projectile speed and tighter turns' end,
  ['psykeeper'] = function() return '[fg]deal [yellow]double[fg] the damage taken by the psykeeper to all enemies' end,
  ['engineer'] = function() return '[fg]drops [yellow]2[fg] additional turrets and grants all turrets [yellow]+50%[fg] damage and attack speed' end,
  ['plague_doctor'] = function() return '[fg]nearby enemies take an additional [yellow]' .. get_character_stat('plague_doctor', 3, 'dmg') .. '[fg] damage per second' end,
  ['barbarian'] = function() return '[fg]stunned enemies also take [yellow]100%[fg] increased damage' end,
  ['juggernaut'] = function() return '[fg]enemies pushed by the juggernaut take [yellow]' .. 4*get_character_stat('juggernaut', 3, 'dmg') .. '[fg] damage if they hit a wall' end,
  ['lich'] = function() return '[fg]chain frost slows enemies hit by [yellow]80%[fg] for [yellow]2[fg] seconds and chains [yellow]+7[fg] times' end,
  ['cryomancer'] = function() return '[fg]enemies are also slowed by [yellow]60%[fg] while in the area' end,
  ['pyromancer'] = function() return '[fg]enemies killed by the pyromancer explode, dealing [yellow]' .. get_character_stat('pyromancer', 3, 'dmg') .. '[fg] AoE damage' end,
  ['corruptor'] = function() return '[fg]spawn [yellow]2[fg] small critters if the corruptor hits an enemy' end,
  ['beastmaster'] = function() return '[fg]spawn [yellow]4[fg] small critters if the beastmaster gets hit' end,
  ['launcher'] = function() return '[fg]enemies launched take [yellow]300%[fg] more damage when they hit walls' end,
  ['jester'] = function() return '[fg]all knives seek enemies and pierce [yellow]2[fg] times' end,
  ['assassin'] = function() return '[fg]poison inflicted from crits deals [yellow]8x[fg] damage' end,
  ['host'] = function() return '[fg][yellow]+100%[fg] critter spawn rate and spawn [yellow]2[fg] critters instead' end,
  ['carver'] = function() return '[fg]carves a tree that creates healing orbs [yellow]twice[fg] as fast' end,
  ['bane'] = function() return "[yellow]100%[fg] increased area for bane's void rifts" end,
  ['psykino'] = function() return '[fg]enemies take [yellow]' .. 4*get_character_stat('psykino', 3, 'dmg') .. '[fg] damage and are pushed away when the area expires' end,
  ['barrager'] = function() return '[fg]every 3rd attack the barrage shoots [yellow]15[fg] projectiles and they push harder' end,
  ['highlander'] = function() return '[fg]quickly repeats the attack [yellow]3[fg] times' end,
  ['fairy'] = function() return '[fg]creates [yellow]2[fg] healing orbs and grants [yellow]2[fg] units [yellow]+100%[fg] attack speed' end,
  ['priest'] = function() return '[fg]picks [yellow]3[fg] units at random and grants them a buff that prevents death once' end,
  ['infestor'] = function() return '[fg][yellow]triples[fg] the number of critters released' end,
  ['flagellant'] = function() return '[yellow]2X[fg] flagellant max HP and grants [yellow]+12%[fg] damage to all allies per cast instead' end,
  ['arcanist'] = function() return '[yellow]+50%[fg] attack speed for the orb and [yellow]2[fg] projectiles are released per cast' end,
  ['illusionist'] = function() return '[yellow]doubles[fg] the number of copies created and they release [yellow]12[fg] projectiles on death' end,
  ['artificer'] = function() return '[fg]automatons shoot and move 50% faster and release [yellow]12[fg] projectiles on death' end,
  ['witch'] = function() return '[fg]the area releases projectiles, each dealing [yellow]' .. get_character_stat('witch', 3, 'dmg') .. '[fg] damage and chaining once' end,
  ['silencer'] = function() return '[fg]the curse also deals [yellow]' .. get_character_stat('silencer', 3, 'dmg') .. '[fg] damage per second' end,
  ['vulcanist'] = function() return '[fg]the number and speed of explosions is [yellow]doubled[fg]' end,
  ['warden'] = function() return '[fg]creates the force field around [yellow]2[fg] units' end,
  ['psychic'] = function() return '[fg]the attack can happen from any distance and repeats once' end,
  ['miner'] = function() return '[fg]release [yellow]8[fg] homing projectiles instead and they pierce twice' end,
  ['merchant'] = function() return '[fg]your first item reroll is always free' end,
  ['usurer'] = function() return '[fg]if the same enemy is cursed [yellow]3[fg] times it takes [yellow]' .. 10*get_character_stat('usurer', 3, 'dmg') .. '[fg] damage' end,
  ['gambler'] = function() return '[yellow]60/40/20%[fg] chance to cast the attack [yellow]2/3/4[fg] times' end,
  ['thief'] = function() return '[fg]if the knife crits it deals [yellow]' .. 10*get_character_stat('thief', 3, 'dmg') .. '[fg] damage, chains [yellow]10[fg] times and grants [yellow]1[fg] gold' end,
}

-- 角色效果描述（灰色版本）
character_effect_descriptions_gray = {
  ['vagrant'] = function() return '[light_bg]+15% attack speed and damage per active class' end,
  ['swordsman'] = function() return "[light_bg]the swordsman's damage is doubled" end,
  ['wizard'] = function() return '[light_bg]the projectile chains 3 times' end,
  ['magician'] = function() return '[light_bg]+50% attack speed every 12 seconds for 6 seconds' end,
  ['archer'] = function() return '[light_bg]the arrow ricochets off walls 3 times' end,
  ['scout'] = function() return '[light_bg]+25% damage per chain and +3 chains' end,
  ['cleric'] = function() return '[light_bg]creates 4 healing orbs' end,
  ['outlaw'] = function() return "[light_bg]+50% outlaw attack speed and his knives seek enemies" end,
  ['blade'] = function() return '[light_bg]deal additional ' .. math.round(get_character_stat('blade', 3, 'dmg')/2, 2) .. ' damage per enemy hit' end,
  ['elementor'] = function() return '[light_bg]slows enemies by 60% for 6 seconds on hit' end,
  ['saboteur'] = function() return '[light_bg]the explosion has 50% chance to crit, increasing in size and dealing 2x damage' end,
  ['bomber'] = function() return '[light_bg]+100% bomb area and damage' end,
  ['stormweaver'] = function() return "[light_bg]chain lightning's trigger area of effect and number of units hit is doubled" end,
  ['sage'] = function() return '[light_bg]when the projectile expires deal ' .. 3*get_character_stat('sage', 3, 'dmg') .. ' damage to all enemies under its influence' end,
  ['squire'] = function() return '[light_bg]+30% damage, attack speed, movement speed and defense to all allies' end,
  ['cannoneer'] = function() return '[light_bg]showers the hit area in 7 additional cannon shots that deal ' .. get_character_stat('cannoneer', 3, 'dmg')/2 .. ' AoE damage' end,
  ['dual_gunner'] = function() return '[light_bg]every 5th attack shoot in rapid succession for 2 seconds' end,
  ['hunter'] = function() return '[light_bg]summons 3 pets and the pets ricochet off walls once' end,
  ['sentry'] = function() return '[light_bg]+50% sentry turret attack speed and the projectiles ricochet twice' end,
  ['chronomancer'] = function() return '[light_bg]enemies take damage over time 50% faster' end,
  ['spellblade'] = function() return '[light_bg]faster projectile speed and tighter turns' end,
  ['psykeeper'] = function() return '[light_bg]deal double the damage taken by the psykeeper to all enemies' end,
  ['engineer'] = function() return '[light_bg]drops 2 additional turrets and grants all turrets +50% damage and attack speed' end,
  ['plague_doctor'] = function() return '[light_bg]nearby enemies take an additional ' .. get_character_stat('plague_doctor', 3, 'dmg') .. ' damage per second' end,
  ['barbarian'] = function() return '[light_bg]stunned enemies also take 100% increased damage' end,
  ['juggernaut'] = function() return '[light_bg]enemies pushed by the juggernaut take ' .. 4*get_character_stat('juggernaut', 3, 'dmg') .. ' damage if they hit a wall' end,
  ['lich'] = function() return '[light_bg]chain frost slows enemies hit by 80% for 2 seconds and chains +7 times' end,
  ['cryomancer'] = function() return '[light_bg]enemies are also slowed by 60% while in the area' end,
  ['pyromancer'] = function() return '[light_bg]enemies killed by the pyromancer explode, dealing ' .. get_character_stat('pyromancer', 3, 'dmg') .. ' AoE damage' end,
  ['corruptor'] = function() return '[light_bg]spawn 2 small critters if the corruptor hits an enemy' end,
  ['beastmaster'] = function() return '[light_bg]spawn 4 small critters if the beastmaster gets hit' end,
  ['launcher'] = function() return '[light_bg]enemies launched take 300% more damage when they hit walls' end,
  ['jester'] = function() return '[light_bg]curses 6 enemies and all knives seek enemies and pierce 2 times' end,
  ['assassin'] = function() return '[light_bg]poison inflicted from crits deals 8x damage' end,
  ['host'] = function() return '[light_bg]+100% critter spawn rate and spawn 2 critters instead' end,
  ['carver'] = function() return '[light_bg]carves a tree that creates healing orbs twice as fast' end,
  ['bane'] = function() return "[light_bg]100% increased area for bane's void rifts" end,
  ['psykino'] = function() return '[light_bg]enemies take ' .. 4*get_character_stat('psykino', 3, 'dmg') .. ' damage and are pushed away when the area expires' end,
  ['barrager'] = function() return '[light_bg]every 3rd attack the barrage shoots 15 projectiles and they push harder' end,
  ['highlander'] = function() return '[light_bg]quickly repeats the attack 3 times' end,
  ['fairy'] = function() return '[light_bg]creates 2 healing orbs and grants 2 units +100% attack speed' end,
  ['priest'] = function() return '[light_bg]picks 3 units at random and grants them a buff that prevents death once' end,
  ['infestor'] = function() return '[light_bg]triples the number of critters released' end,
  ['flagellant'] = function() return '[light_bg]2X flagellant max HP and grants +12% damage to all allies per cast instead' end,
  ['arcanist'] = function() return '[light_bg]+50% attack speed for the orb and 2 projectiles are released per cast' end,
  ['illusionist'] = function() return '[light_bg]doubles the number of copies created and they release 12 projectiles on death' end,
  ['artificer'] = function() return '[light_bg]automatons shoot and move 50% faster and release 12 projectiles on death' end,
  ['witch'] = function() return '[light_bg]the area periodically releases projectiles, each dealing ' .. get_character_stat('witch', 3, 'dmg') .. ' damage and chaining once' end,
  ['silencer'] = function() return '[light_bg]the curse also deals ' .. get_character_stat('silencer', 3, 'dmg') .. ' damage per second' end,
  ['vulcanist'] = function() return '[light_bg]the number and speed of explosions is doubled' end,
  ['warden'] = function() return '[light_bg]creates the force field around 2 units' end,
  ['psychic'] = function() return '[light_bg]the attack can happen from any distance and repeats once' end,
  ['miner'] = function() return '[light_bg]release 8 homing projectiles instead and they pierce twice' end,
  ['merchant'] = function() return '[light_bg]your first item reroll is always free' end,
  ['usurer'] = function() return '[light_bg]if the same enemy is cursed 3 times it takes ' .. 10*get_character_stat('usurer', 3, 'dmg') .. ' damage' end,
  ['gambler'] = function() return '[light_bg]60/40/20% chance to cast the attack 2/3/4 times' end,
  ['thief'] = function() return '[light_bg]if the knife crits it deals ' .. 10*get_character_stat('thief', 3, 'dmg') .. ' damage, chains 10 times and grants 1 gold' end,
}

-- 角色属性统计
character_stats = {
  ['vagrant'] = function(lvl) return get_character_stat_string('vagrant', lvl) end,
  ['swordsman'] = function(lvl) return get_character_stat_string('swordsman', lvl) end,
  ['wizard'] = function(lvl) return get_character_stat_string('wizard', lvl) end,
  ['magician'] = function(lvl) return get_character_stat_string('magician', lvl) end,
  ['archer'] = function(lvl) return get_character_stat_string('archer', lvl) end,
  ['scout'] = function(lvl) return get_character_stat_string('scout', lvl) end,
  ['cleric'] = function(lvl) return get_character_stat_string('cleric', lvl) end,
  ['outlaw'] = function(lvl) return get_character_stat_string('outlaw', lvl) end,
  ['blade'] = function(lvl) return get_character_stat_string('blade', lvl) end,
  ['elementor'] = function(lvl) return get_character_stat_string('elementor', lvl) end,
  ['saboteur'] = function(lvl) return get_character_stat_string('saboteur', lvl) end,
  ['bomber'] = function(lvl) return get_character_stat_string('bomber', lvl) end,
  ['stormweaver'] = function(lvl) return get_character_stat_string('stormweaver', lvl) end,
  ['sage'] = function(lvl) return get_character_stat_string('sage', lvl) end,
  ['squire'] = function(lvl) return get_character_stat_string('squire', lvl) end,
  ['cannoneer'] = function(lvl) return get_character_stat_string('cannoneer', lvl) end,
  ['dual_gunner'] = function(lvl) return get_character_stat_string('dual_gunner', lvl) end,
  ['hunter'] = function(lvl) return get_character_stat_string('hunter', lvl) end,
  ['sentry'] = function(lvl) return get_character_stat_string('sentry', lvl) end,
  ['chronomancer'] = function(lvl) return get_character_stat_string('chronomancer', lvl) end,
  ['spellblade'] = function(lvl) return get_character_stat_string('spellblade', lvl) end,
  ['psykeeper'] = function(lvl) return get_character_stat_string('psykeeper', lvl) end,
  ['engineer'] = function(lvl) return get_character_stat_string('engineer', lvl) end,
  ['plague_doctor'] = function(lvl) return get_character_stat_string('plague_doctor', lvl) end,
  ['barbarian'] = function(lvl) return get_character_stat_string('barbarian', lvl) end,
  ['juggernaut'] = function(lvl) return get_character_stat_string('juggernaut', lvl) end,
  ['lich'] = function(lvl) return get_character_stat_string('lich', lvl) end,
  ['cryomancer'] = function(lvl) return get_character_stat_string('cryomancer', lvl) end,
  ['pyromancer'] = function(lvl) return get_character_stat_string('pyromancer', lvl) end,
  ['corruptor'] = function(lvl) return get_character_stat_string('corruptor', lvl) end,
  ['beastmaster'] = function(lvl) return get_character_stat_string('beastmaster', lvl) end,
  ['launcher'] = function(lvl) return get_character_stat_string('launcher', lvl) end,
  ['jester'] = function(lvl) return get_character_stat_string('jester', lvl) end,
  ['assassin'] = function(lvl) return get_character_stat_string('assassin', lvl) end,
  ['host'] = function(lvl) return get_character_stat_string('host', lvl) end,
  ['carver'] = function(lvl) return get_character_stat_string('carver', lvl) end,
  ['bane'] = function(lvl) return get_character_stat_string('bane', lvl) end,
  ['psykino'] = function(lvl) return get_character_stat_string('psykino', lvl) end,
  ['barrager'] = function(lvl) return get_character_stat_string('barrager', lvl) end,
  ['highlander'] = function(lvl) return get_character_stat_string('highlander', lvl) end,
  ['fairy'] = function(lvl) return get_character_stat_string('fairy', lvl) end,
  ['priest'] = function(lvl) return get_character_stat_string('priest', lvl) end,
  ['infestor'] = function(lvl) return get_character_stat_string('infestor', lvl) end,
  ['flagellant'] = function(lvl) return get_character_stat_string('flagellant', lvl) end,
  ['arcanist'] = function(lvl) return get_character_stat_string('arcanist', lvl) end,
  ['illusionist'] = function(lvl) return get_character_stat_string('illusionist', lvl) end,
  ['artificer'] = function(lvl) return get_character_stat_string('artificer', lvl) end,
  ['witch'] = function(lvl) return get_character_stat_string('witch', lvl) end,
  ['silencer'] = function(lvl) return get_character_stat_string('silencer', lvl) end,
  ['vulcanist'] = function(lvl) return get_character_stat_string('vulcanist', lvl) end,
  ['warden'] = function(lvl) return get_character_stat_string('warden', lvl) end,
  ['psychic'] = function(lvl) return get_character_stat_string('psychic', lvl) end,
  ['miner'] = function(lvl) return get_character_stat_string('miner', lvl) end,
  ['merchant'] = function(lvl) return get_character_stat_string('merchant', lvl) end,
  ['usurer'] = function(lvl) return get_character_stat_string('usurer', lvl) end,
  ['gambler'] = function(lvl) return get_character_stat_string('gambler', lvl) end,
  ['thief'] = function(lvl) return get_character_stat_string('thief', lvl) end,
}

-- 职业属性乘数
class_stat_multipliers = {
  ['ranger'] = {hp = 1, dmg = 1.2, aspd = 1.5, area_dmg = 1, area_size = 1, def = 0.9, mvspd = 1.2},
  ['warrior'] = {hp = 1.4, dmg = 1.1, aspd = 0.9, area_dmg = 1, area_size = 1, def = 1.25, mvspd = 0.9},
  ['mage'] = {hp = 0.6, dmg = 1.4, aspd = 1, area_dmg = 1.25, area_size = 1.2, def = 0.75, mvspd = 1},
  ['rogue'] = {hp = 0.8, dmg = 1.3, aspd = 1.1, area_dmg = 0.6, area_size = 0.6, def = 0.8, mvspd = 1.4},
  ['healer'] = {hp = 1.2, dmg = 1, aspd = 0.5, area_dmg = 1, area_size = 1, def = 1.2, mvspd = 1},
  ['enchanter'] = {hp = 1.2, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1.2, mvspd = 1.2},
  ['nuker'] = {hp = 0.9, dmg = 1, aspd = 0.75, area_dmg = 1.5, area_size = 1.5, def = 1, mvspd = 1},
  ['conjurer'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 1},
  ['psyker'] = {hp = 1.5, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 0.5, mvspd = 1},
  ['curser'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 0.75, mvspd = 1},
  ['forcer'] = {hp = 1.25, dmg = 1.1, aspd = 0.9, area_dmg = 0.75, area_size = 0.75, def = 1.2, mvspd = 1},
  ['swarmer'] = {hp = 1.2, dmg = 1, aspd = 1.25, area_dmg = 1, area_size = 1, def = 0.75, mvspd = 0.75},
  ['voider'] = {hp = 0.75, dmg = 1.3, aspd = 1, area_dmg = 0.8, area_size = 0.75, def = 0.6, mvspd = 0.8},
  ['sorcerer'] = {hp = 0.8, dmg = 1.3, aspd = 1, area_dmg = 1.2, area_size = 1, def = 0.8, mvspd = 1},
  ['mercenary'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 1},
  ['explorer'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 1.25},
  ['seeker'] = {hp = 0.5, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 0.3},
  ['mini_boss'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 0.3},
  ['enemy_critter'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 0.5},
  ['saboteur'] = {hp = 1, dmg = 1, aspd = 1, area_dmg = 1, area_size = 1, def = 1, mvspd = 1.4},
}

-- 职业描述函数辅助工具
local ylb1 = function(lvl)
  if lvl == 3 then return 'light_bg'
  elseif lvl == 2 then return 'light_bg'
  elseif lvl == 1 then return 'yellow'
  else return 'light_bg' end
end
local ylb2 = function(lvl)
  if lvl == 3 then return 'light_bg'
  elseif lvl == 2 then return 'yellow'
  else return 'light_bg' end
end
local ylb3 = function(lvl)
  if lvl == 3 then return 'yellow'
  else return 'light_bg' end
end

-- 职业描述
class_descriptions = {
  ['ranger'] = function(lvl) return '[' .. ylb1(lvl) .. ']3[light_bg]/[' .. ylb2(lvl) .. ']6 [fg]- [' .. ylb1(lvl) .. ']8%[light_bg]/[' .. ylb2(lvl) .. ']16% [fg]chance to release a barrage on attack to allied rangers' end,
  ['warrior'] = function(lvl) return '[' .. ylb1(lvl) .. ']3[light_bg]/[' .. ylb2(lvl) .. ']6 [fg]- [' .. ylb1(lvl) .. ']+25[light_bg]/[' .. ylb2(lvl) .. ']+50 [fg]defense to allied warriors' end,
  ['mage'] = function(lvl) return '[' .. ylb1(lvl) .. ']3[light_bg]/[' .. ylb2(lvl) .. ']6 [fg]- [' .. ylb1(lvl) .. ']-15[light_bg]/[' .. ylb2(lvl) .. ']-30 [fg]enemy defense' end,
  ['rogue'] = function(lvl) return '[' .. ylb1(lvl) .. ']3[light_bg]/[' .. ylb2(lvl) .. ']6 [fg]- [' .. ylb1(lvl) .. ']15%[light_bg]/[' .. ylb2(lvl) .. ']30% [fg]chance to crit to allied rogues, dealing [yellow]4x[] damage' end,
  ['healer'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+15%[light_bg]/[' .. ylb2(lvl) .. ']+30% [fg] chance to create [yellow]+1[fg] healing orb on healing orb creation' end,
  ['enchanter'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+15%[light_bg]/[' .. ylb2(lvl) .. ']+25% [fg]damage to all allies' end,
  ['nuker'] = function(lvl) return '[' .. ylb1(lvl) .. ']3[light_bg]/[' .. ylb2(lvl) .. ']6 [fg]- [' .. ylb1(lvl) .. ']+15%[light_bg]/[' .. ylb2(lvl) .. ']+25% [fg]area damage and size to allied nukers' end,
  ['conjurer'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+25%[light_bg]/[' .. ylb2(lvl) .. ']+50% [fg]construct damage and duration' end,
  ['psyker'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+2[light_bg]/[' .. ylb2(lvl) .. ']+4 [fg]total psyker orbs and [yellow]+1[fg] orb for each psyker' end,
  ['curser'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+1[light_bg]/[' .. ylb2(lvl) .. ']+3 [fg]max curse targets to allied cursers' end,
  ['forcer'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+25%[light_bg]/[' .. ylb2(lvl) .. ']+50% [fg]knockback force to all allies' end,
  ['swarmer'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+1[light_bg]/[' .. ylb2(lvl) .. ']+3 [fg]hits to critters' end,
  ['voider'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+20%[light_bg]/[' .. ylb2(lvl) .. ']+40% [fg]damage over time to allied voiders' end,
  ['sorcerer'] = function(lvl)
    return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4[light_bg]/[' .. ylb3(lvl) .. ']6 [fg]- sorcerers repeat their attacks once every [' ..
      ylb1(lvl) .. ']4[light_bg]/[' .. ylb2(lvl) .. ']3[light_bg]/[' .. ylb3(lvl) .. ']2[fg] attacks'
  end,
  ['mercenary'] = function(lvl) return '[' .. ylb1(lvl) .. ']2[light_bg]/[' .. ylb2(lvl) .. ']4 [fg]- [' .. ylb1(lvl) .. ']+8%[light_bg]/[' .. ylb2(lvl) .. ']+16% [fg]chance for enemies to drop gold on death' end,
  ['explorer'] = function(lvl) return '[yellow]+15%[fg] attack speed and damage per active class to allied explorers' end,
}

-- 层级到角色映射
tier_to_characters = {
  [1] = {'vagrant', 'swordsman', 'magician', 'archer', 'scout', 'cleric', 'arcanist', 'merchant'},
  [2] = {'wizard', 'bomber', 'sage', 'squire', 'dual_gunner', 'sentry', 'chronomancer', 'barbarian', 'cryomancer', 'beastmaster', 'jester', 'carver', 'psychic', 'witch', 'silencer', 'outlaw', 'miner'},
  [3] = {'elementor', 'stormweaver', 'spellblade', 'psykeeper', 'engineer', 'juggernaut', 'pyromancer', 'host', 'assassin', 'bane', 'barrager', 'infestor', 'flagellant', 'artificer', 'usurer', 'gambler'},
  [4] = {'priest', 'highlander', 'psykino', 'fairy', 'blade', 'plague_doctor', 'cannoneer', 'vulcanist', 'warden', 'corruptor', 'thief'},
}

-- 角色层级定义
character_tiers = {
  ['vagrant'] = 1,
  ['swordsman'] = 1,
  ['magician'] = 1,
  ['archer'] = 1,
  ['scout'] = 1,
  ['cleric'] = 1,
  ['outlaw'] = 2,
  ['blade'] = 4,
  ['elementor'] = 3,
  -- ['saboteur'] = 2,
  ['bomber'] = 2,
  ['wizard'] = 2,
  ['stormweaver'] = 3,
  ['sage'] = 2,
  ['squire'] = 2,
  ['cannoneer'] = 4,
  ['dual_gunner'] = 2,
  -- ['hunter'] = 2,
  ['sentry'] = 2,
  ['chronomancer'] = 2,
  ['spellblade'] = 3,
  ['psykeeper'] = 3,
  ['engineer'] = 3,
  ['plague_doctor'] = 4,
  ['barbarian'] = 2,
  ['juggernaut'] = 3,
  -- ['lich'] = 4,
  ['cryomancer'] = 2,
  ['pyromancer'] = 3,
  ['corruptor'] = 4,
  ['beastmaster'] = 2,
  -- ['launcher'] = 2,
  ['jester'] = 2,
  ['assassin'] = 3,
  ['host'] = 3,
  ['carver'] = 2,
  ['bane'] = 3,
  ['psykino'] = 4,
  ['barrager'] = 3,
  ['highlander'] = 4,
  ['fairy'] = 4,
  ['priest'] = 4,
  ['infestor'] = 3,
  ['flagellant'] = 3,
  ['arcanist'] = 1,
  -- ['illusionist'] = 3,
  ['artificer'] = 3,
  ['witch'] = 2,
  ['silencer'] = 2,
  ['vulcanist'] = 4,
  ['warden'] = 4,
  ['psychic'] = 2,
  ['miner'] = 2,
  ['merchant'] = 1,
  ['usurer'] = 3,
  ['gambler'] = 3,
  ['thief'] = 4,
}

-- 功能：判断角色是否会发射投射物
-- 参数：character(string) - 角色名称
-- 返回：boolean - 是否发射投射物
launches_projectiles = function(character)
  local classes = {'vagrant', 'archer', 'scout', 'outlaw', 'blade', 'wizard', 'cannoneer', 'dual_gunner', 'hunter', 'spellblade', 'engineer', 'corruptor', 'beastmaster', 'jester', 'assassin', 'barrager',
    'arcanist', 'illusionist', 'artificer', 'miner', 'thief', 'sentry'}
  return table.any(classes, function(v) return v == character end)
end