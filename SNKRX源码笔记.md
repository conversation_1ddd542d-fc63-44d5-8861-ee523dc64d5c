作者：道莫如
更新日期：2025-1-1
# 一、引言
SNKRX汉化版本路径："/Users/<USER>/Documents/LoveProjects/DaomoruSNKRX_NoSteam_Chinese"
《SNKRX》的github地址：https://github.com/a327ex/SNKRX
这是一款有肉鸽元素的自动射击游戏。据说是《小丑牌》的灵感来源。
《SNKRX》在github上以MIT协议发布，但也上架了Steam。本笔记随附的源码将Steam验证给注释掉了，并添加了汉化。随附的源码仅供学习使用，请读者尽量购买正版游玩。游玩时请记得打开鼠标操作，用键盘操作的话体验非常差。
因为《SNKRX》是一款商业运行的游戏，所以其各方面都值得学习借鉴：如何在游戏内加入Steam支持、伤害计算公式等等。我们主要学习《SNKRX》的“打击感”的实现代码，与肉鸽道具组合的实现代码。
版权声明：本阅读笔记和所附源码的汉化、中文注释的版权归作者道莫如所有，未经允许，禁止分享。
# 二、整体架构
我们来看一下《SNKRX》的整体架构：

| 文件 | 内容 |
|:------|:-----|
|main.lua| 游戏数据的加载、游戏主循环|
|mainmenu.lua|开始场景|
|buy_screen.lua|购买场景|
|arena.lua|对战场景|
|player.lua|贪吃蛇的一段（不是整条蛇，只是一段）|
|enemies.lua|各种敌怪|
|objects.lua|在对战场景中显示的一些杂项|
|shared.lua|共享的一些函数和类|
|engine文件夹|作者自己写的极小游戏引擎|
游戏只有三个场景，非常简单。
《SNKRX》使用了Love2d这个游戏引擎。原作者又在Love2d上封装了一层自己的代码，添加了一些功能，也就是engine文件夹。这是原作者以前就在用的模板，在该地址可以找到：
https://github.com/a327ex/a327ex-template
其实《SNKRX》还有一个前身：
https://github.com/a327ex/BYTEPATH
原作者为其写了详细的教程，读者可以参考一下。
《SNKRX》拥有完整的开发日志devlog.md、以及github提交记录。有耐心的读者可以从最开始的commit一步步往后看。但是该游戏比较简单，我们就直接看最新版。
# 三、程序入口
《SNKRX》的程序入口写得很杂，我们捡重要的地方说一下。
程序入口是main.lua的love.run()，里面调用了原作者自己写的engine.run(config)，在engine/init.lua中。可以看到里面调用了定义在main.lua中的init()、update()和draw()。
love.run()的文档在https://love2d.org/wiki/love.run
>注记：Love2d引擎的所有API都是以“love.”开头。

love.run()返回一个函数，love2d引擎会每帧调用一次这个被返回的函数，这个被返回的函数就是游戏的主循环。
>注记：
>engine_run(config)这个函数值得我们学习的一个小知识是这一行代码：
>```Lua 
>if love.timer then love.timer.sleep(0.001) end
>```
>这段代码在love.run()的文档的最下面有Notes：为什么需要这么一段Delay？
>https://love2d.org/wiki/love.run

另外engine/init.lua还值得我们学习的部分，就是其如何实现了像素风格。文档在这里：https://github.com/a327ex/blog/issues/19 在Game Size一段。

main.lua中，init()函数调用shared.lua的shared_init()函数，把要用到的颜色设为全局常量。
>注记：游戏中到处使用全局的颜色常量，如bg、fg等。读者用编辑器找其定义之处是找不到的。因为全局的颜色常量都是动态添加在_G里的，就在shared_init()函数开头。请读者注意。

然后init()又把所有的音乐、图片、游戏数据、文字描述等也都设为了全局常量。这样好与不好请读者见仁见智。
>注记：Lua的变量定义前不加local的话，则默认是全局变量。例如mian=Main()，main实例就是全局变量，掌管着游戏场景的转换。

init()函数最后初始化了Main这个类，然后进入mainmenu：
```Lua
  main = Main()
  main:add(MainMenu'mainmenu')
  main:go_to('mainmenu')
```
>注记：在Lua中，函数若只有一个参数且参数是字面字符串或table构造式，则圆括号可有可无。
>例如MainMenu'mainmenu'，这个函数调用就没有加圆括号。
# 四、游戏场景的转换
我们来看mainmenu.lua文件，可以看到MainMenu实现了State。
>注记：
>lua没有官方的面向对象语法。原作者自己做了一套混入（Mixin）的框架，在engine/game/object.lua中。原作者写的这个框架非常好，《小丑牌》也直接拿来用了。
>混入（Mixin）的讲解在：https://github.com/a327ex/blog/issues/2
>在这里也可以看到讲解：https://github.com/a327ex/emoji-merge?tab=readme-ov-file#mixins
>混入（Mixin）没什么特别的，跟其他语言的面向对象有些相似。所以我们就还是用面向对象的术语来讲解。

打开engine/game/state.lua文件，Main也实现了State，但这个Main类地位特殊，它管理着游戏场景的转换。
Main的实例main使用Main:add(state)方法添加一个游戏场景，然后使用Main:go_to(state，...)方法激活这个游戏场景。
所有游戏场景转换都是先后调用这两个方法，读者可以全局搜索这两个方法，了解游戏场景的转换。
# 五、游戏主循环的update和draw
回到main.lua文件，在全局函数update(dt)和draw()中，可以看到main:update(dt)和main:draw()，这两个方法内部调用当前游戏场景的update(dt)和draw()。例如进入mainmenu后调用的就是MainMenu:update(dt)和MainMenu:draw()。
注意，main实例有一个属性：current。游戏各处的代码都用main.current来获取当前的游戏场景。
main.lua文件中的全局函数draw()中调用了shared_draw()函数。shared_draw()用shadow_shader将main_canvas的画面内容渲染成阴影，增加游戏画面的立体感。shadow_shader定义在shared_init()中。对shader感兴趣的读者可以注意一下。
# 六、mainmenu场景与Group类
所有游戏场景都会有多个Group实例，主要有四个：self.floor、self.main、self.effects、self.ui（buy_screen没有self.floor）。这四个Group实例约定的用途可以看engine/game/group.lua的注释。
Group类的用途是管理一个游戏场景中被分成组（group）的小组件。一些小组件有相同的物理行为、摄像机行为，便可被分到同一Group。例如mainmenu中四面墙（Wall）都被添加到self.main这个Group实例中；arena_run键、options键、quit键都被添加到self.main_ui这个Group实例中。
小组件被添加到一个Group实例后，它的update()和draw()便由这个Group代管，不必再自行调用了。
## 小组件是如何被添加到Group中的？
小组件一般都实现了GameObject。GemeObject类有大多数组件的共同功能：触发器、打击感效果器，以及“被添加到Group实例中”的功能。前两个功能非常重要，我们后面再看。
现在来看小组件是如何被添加进Group实例中的。打开engine/game/gameobject.lua，在GameObject:init_game_object(args)中：
```Lua
GameObject = Object:extend()
function GameObject:init_game_object(args)
  for k, v in pairs(args or {}) do self[k] = v end
  if self.group then self.group:add(self) end
```
可以看到，args的所有键值对都被添加为self的属性。然后检测args的group键是否存在，存在则把self添加到这个group中。
举例来说，mainmenu.lua文件中，Main:on_enter(from)内：
```Lua
  Wall{group = self.main, vertices = math.to_rectangle_vertices(-40, -40, self.x1, gh + 40), color = bg[-1]}
  Wall{group = self.main, vertices = math.to_rectangle_vertices(self.x2, -40, gw + 40, gh + 40), color = bg[-1]}
  Wall{group = self.main, vertices = math.to_rectangle_vertices(self.x1, -40, self.x2, self.y1), color = bg[-1]}
  Wall{group = self.main, vertices = math.to_rectangle_vertices(self.x1, self.y2, self.x2, gh + 40), color = bg[-1]}
```
这四行，就把四个Wall，也是四个GameObject实例给添加到self.main这个Group实例中了。
>注记：再次提醒，在Lua中，函数若只有一个参数且参数是字面字符串或table构造式，则圆括号可有可无。这里Wall()的调用就没加圆括号。
# 七、buy_screen场景与汉化
在mainmenu.lua中搜索“main:go_to”,可以看到只有一行，在self.arena_run_button的定义中：
```Lua
      system.save_state()
      main:add(BuyScreen'buy_screen')
      main:go_to('buy_screen', run.level or 1, run.loop or 0, run.units or {}, passives, run.shop_level or 1, run.shop_xp or 0)
```
>注记：本游戏中所有按键检测的代码，都是在各个小组件的update(dt)方法中。没有例外。例如input.f.pressed是检测“f”键是否被按下。

打开buy_screen.lua文件，最上面是笔者的汉化代码。
>注记：
>所有以‘daomoru’开头的都是笔者添加的代码。
>《SNKRX》中使用font:getWidth(text)这一love2d的API来获取字符串的宽度，但这一API对中文字符无法工作。所以笔者将汉化文本放在游戏窗口的最上面或最下边，而不是直接替换游戏内的文本。这样也方便玩家进行中英文对照。
>获取中文字符串宽度的解决方案可以看《小丑牌》的代码。

接下来用笔者的汉化代码作例子，来解读buy_screen.lua的代码。
笔者的汉化组件类的名称叫DaomoruChinese，首先要实现GameObject，这样才能将DaomoruChinese的实例添加到一个Group实例中。
然后在main.lua中搜索descriptions，有四个table：

|table名| 内容|
|:-----|:------|
|character_descriptions|英雄技能描述|
|character_effect_descriptions|三级英雄技能描述|
|class_descriptions|角色组合效果描述|
|passive_descriptions|饰品效果描述|
1、将这四个table的内容汉化，并放在前面添加了前缀“daomoru”的table中。例如character_descriptions添加前缀成为daomoru_character_descriptions。
2、在buy_screen.lua中，搜索对应的descriptions。然后在对应的Part类、Card类或Icon类的on_mouse_enter()方法中初始化一个DaomoruChinese。例如在CharacterPart:on_mouse_enter()方法中的最后添加如下两行代码：
```Lua
  self.daomoruChineseObject1 = DaomoruChinese({group = main.current.ui}, daomoru_character_descriptions[self.character](self.level), true)
  self.daomoruChineseObject2 = DaomoruChinese({group = main.current.ui}, daomoru_character_effect_descriptions[self.character](), false)
```
如此，在游戏中，当鼠标指向这个CharacterPart时，汉化文本便会出现。
3、接下来还要在对应的on_mouse_exit()和die()方法调用DaomoruChinese:deactive()，以销毁DaomoruChinese组件。DaomoruChinese:deactive()会将dead属性设为true。然后Group:update()内会轮询dead属性，一旦dead为true，就删除该组件，也就不再显示了。例如在CharacterPart:on_mouse_exit()和CharacterPart:die()中，添加如下代码：
```Lua
  if self.daomoruChineseObject1 then
    self.daomoruChineseObject1:deactivate()
    self.daomoruChineseObject1 = nil
  end
  if self.daomoruChineseObject2 then
    self.daomoruChineseObject2:deactivate()
    self.daomoruChineseObject2 = nil
  end
```
on_mouse_exit()是在鼠标移开时调用的；die()是在点击鼠标左键买进、或右键卖出时调用的。这两种情况都要销毁汉化文本。
>注记：passive和item指的是同一个东西。
# 八、arena场景 与 Trigger
GoButton:update(dt)内有打开arena场景的代码。
我们现在进入arena场景。打开arena.lua文件。arena大量使用Trigger来产生敌人，Trigger即触发器。典型的方法有：
Trigger:after(delay, action, tag)：delay秒后调用action函数。
Trigger:every(delay, action, times, after, tag)：每delay秒调用一次action函数，调用times次。最后一次调用完action函数后，调用after函数。
这两个函数也都可以用作轮询函数，即当delay是一个函数时，Trigger实例会在Trigger:update(dt)中轮询dalay函数是否返回true，返回true时调用action函数。
Trigger类的函数有很多，但都有详细的注释。笔者也在arena代码中做了一些注释。都是比较简单的代码，所以请读者自行阅读。
>注记：笔者的注释都以中文句号结尾，搜索“。”即可看到笔者所加的注释。
# 九、Player类 与 肉鸽道具组合玩法的实现
我们来看《SNKRX》的肉鸽道具组合玩法是如何实现的。
## 9.1、passive
passive是饰品组合。每通过一个大关卡，就可获得一个饰品，显示在buy_screen的item栏。
打开player.lua文件，看到如下代码：
```Lua
function Player:init(args)
  self:init_game_object(args)
  self:init_unit()
  --将所拥有的passive的名称添加为self的属性。
  if self.passives then for k, v in pairs(self.passives) do self[v.passive] = v.level end end
```
可以看到，这段代码将玩家当前所拥有的passive添加为player的属性。然后在代码的各处检测是否有某某passive这个属性，有的话就执行相应代码。这里的“各处”，真的是到处都有：Player的update(dt)、on_collision_enter()……enmies.lua中Seeker的init(args)、update(dt)、on_collision_enter()等等很多地方都有。
笔者在各个passive的实现之处做了注释，只需要全局搜索“--passive:”，即可看到笔者在代码实现处做的注释。
或者全局搜索对应passive名称，即可找到对应实现代码。
例如，在enemies.lua中搜索“player.intimidation”，即可找到“敌人最大血量-10/20/30%”这个passive的实现代码；
又例如在player.lua中搜索“self.resonance”，即可找到“'所有单位的每次范围伤害+3/5/7%'”这个passive的实现代码。
>注记：有些passive和class的实现代码有多处，笔者可能只标出了一处。

肉鸽的实现还可看文档：https://github.com/a327ex/blog/issues/25
## 9.2、class
class是角色组合，或者可以翻译为职业组合。有多个英雄拥有同一class时，就可获得更强的或额外的能力。class显示在buy_screen的class栏。
打开arena.lua，看到如下代码：
```Lua
  local class_levels = get_class_levels(units) --更新class组合的level。
  self.ranger_level = class_levels.ranger
  self.warrior_level = class_levels.warrior
  self.mage_level = class_levels.mage
  self.rogue_level = class_levels.rogue
  self.nuker_level = class_levels.nuker
  self.curser_level = class_levels.curser
  self.forcer_level = class_levels.forcer
  self.swarmer_level = class_levels.swarmer
  self.voider_level = class_levels.voider
  self.enchanter_level = class_levels.enchanter
  self.healer_level = class_levels.healer
  self.psyker_level = class_levels.psyker
  self.conjurer_level = class_levels.conjurer
  self.sorcerer_level = class_levels.sorcerer
  self.mercenary_level = class_levels.mercenary
```
跟passive类似，原作者将class组合的level设置为arena场景的属性。检测对应的main.current.<class名称>\_level，即可得知角色组合的level。
>注记：再次提醒，main.current指向当前的游戏场景。

角色组合的实现同样散落在各处，搜索“--class:”即可看到笔者在角色组合实现处添加的注释。
或者全局搜索“main.current.<class名称>\_level”，即可找到角色组合实现的代码。
例如，全局搜索“main.current.mage_level”，即可找到“法师角色使敌人防御-15/-30”这个mage的角色组合的实现代码。
>注记：
>但有一个角色组合特殊，即explorer这个class组合。这个class组合没有多个等级。全局搜索“table.any(self.classes, function(v) return v == 'explorer' end)”可以找到其实现代码。

# 十、打击感如何实现
《SNKRX》打击感的实现，是该游戏最值得学习的部分。
《SNKRX》中有两种非常重要的打击感效果：弹簧效果和闪烁效果。
所有具有这两种效果的object都是实现了GameObject，由GameObject来统一管理弹簧和闪烁效果。
打开engine/game/gameobject.lua文件：
```Lua
  self.springs = Springs()
  self.flashes = Flashes()
  self.hfx = HitFX(self)
  self.spring = Spring(1)
```
这几个属性中：self.springs和self.spring实现弹簧效果，self.flashes实现闪烁效果。self.hfx统一管理着self.springs和self.flashes，self.springs和self.flashes不单独使用。
在看这两种效果之前，我们先来看摄像机抖动效果。
## 10.1、Camera类实现摄像机抖动
打开engine/game/group.lua，看到如下代码：
```Lua
-- scroll_factor_x and scroll_factor_y can be used for parallaxing, they should be values between 0 and 1
-- The closer to 0, the more of a parallaxing effect there will be.
function Group:draw(scroll_factor_x, scroll_factor_y)
  if self.camera then self.camera:attach(scroll_factor_x, scroll_factor_y) end
    for _, object in ipairs(self.objects) do
      if not object.hidden then
        object:draw()
      end
    end
  if self.camera then self.camera:detach() end
end
```
可以看到，在真正的绘制objects之前，先调用了Camera:attach(scroll_factor_x, scroll_factor_y)，结束绘制后又调用Camera:detach()。
Camera:attach(scroll_factor_x, scroll_factor_y)方法用于保存坐标系，并建立新的坐标系；Camera:detach方法用于还原被保存的坐标系。
我们来看Camera:attach(scroll_factor_x, scroll_factor_y)的代码：
```Lua
function Camera:attach(scroll_factor_x, scroll_factor_y)
  self.bx, self.by = self.x, self.y
  self.x = self.bx*(scroll_factor_x or 1)
  self.y = self.by*(scroll_factor_y or scroll_factor_x or 1)
  love.graphics.push()
  love.graphics.translate(self.w/2, self.h/2)
  if not self.ignore_scale then love.graphics.scale(self.sx, self.sy) end
  love.graphics.rotate(self.r)
  love.graphics.translate(-self.x*(scroll_factor_x or 1), -self.y*(scroll_factor_y or 1))
end
```
可以看到，这个函数内调用love.graphics.push()，这个API的作用是保存当前的坐标系。love.graphics.tanslate()、rotate()以及scale()会根据self.x、self.y以及self.r建立新的坐标系。
一个场景如果调用了Camera:shake(intensity, duration, frequency)之类的抖动摄像机的函数的话，会在Camera:update(dt)中改变Camera的self.x和self.y。
这样，由于之前保存又建立了坐标系，Group再绘制时就会以新的坐标系进行绘制。**==不断地改变Camera的self.x和self.y，然后Group不断以新的坐标系进行绘制，也就实现了摄像机抖动效果。==**
请按10.6汇总一节中的代码做实验，取消掉以下两处代码的注释：
```Lua
    --请分别取消下面两行。
    --   camera:shake(4, 0.5)
    --   camera:spring_shake(20, math.pi - 10) --可以看到蛇跟文字一起抖向一边，而其他的按键UI不会动。
```
```Lua
--看看摄相机抖动、Flashes、Spring以及慢动作是什么效果。
--   daomoruHfxExample{group = self.main}
```
然后进入游戏的mainmenu场景，按“f”键即可看到摄像机抖动效果：
camera:shake(4, 0.5)使蛇跟文字一起震动。
camera:spring_shake(20, math.pi - 10)使蛇跟文字一起抖向一边。
而其他的按键UI不会动。因为蛇跟文字都加入了self.main这个Group实例当中，而其他的按键UI加入的是self.main_ui这个Group。

Camera的文档还可以看这里：https://github.com/a327ex/emoji-merge?tab=readme-ov-file#camera
## 10.2、Flashes
Flashes类内有个被Trigger控制的“f”属性。绘制object的颜色时，检测“f”这个属性，来决定颜色的绘制，以实现闪烁的效果。
例如，player.lua中Tree的绘制：
```Lua
function Tree:draw()
  if self.hidden then return end

  graphics.push(self.x, self.y, math.pi/4, self.spring.x, self.spring.x)
    graphics.rectangle(self.x, self.y, 1.5*self.shape.w, 4, 2, 2, self.hfx.hit.f and fg[0] or self.color)
    graphics.rectangle(self.x, self.y, 4, 1.5*self.shape.h, 2, 2, self.hfx.hit.f and fg[0] or self.color)
  graphics.pop()
```
可以看到，绘制Tree填入颜色参数的时候会检测self.hfx.hit.f，如果为真，颜色为fg[0]，假则为self.color。
>注记：Lua中`A and B or C`这种结构相当于C语言中的三目运算符。 

## 10.3、Springs
Spring的工作原理的文档请看：https://github.com/a327ex/blog/issues/60
Spring的用法跟Camera类似，也是靠改变坐标系来实现的。调用Spring:pull(f, k, d)可以改变Spring.x，然后再用Spring.x来改变坐标系。例如Tree:init(args)中的一行代码：
```Lua
  self.t:tween(0.05, self, {rs = args.rs}, math.cubic_in_out, function() self.spring:pull(0.15) end)
```
spring:pull(f, k, d)会改变Spring.x。然后在真正绘制Tree之前，先调用graphics.push(x, y, r, sx, sy)保存当前坐标系、建立新的坐标系：
```Lua
function Tree:draw()
  if self.hidden then return end
  graphics.push(self.x, self.y, math.pi/4, self.spring.x, self.spring.x)
    graphics.rectangle(self.x, self.y, 1.5*self.shape.w, 4, 2, 2, self.hfx.hit.f and fg[0] or self.color)
    graphics.rectangle(self.x, self.y, 4, 1.5*self.shape.h, 2, 2, self.hfx.hit.f and fg[0] or self.color)
  graphics.pop()
```
我们再来看graphics.push(x, y, r, sx, sy)的代码：
```Lua
-- All operations after this is called will be affected by the transform.
function graphics.push(x, y, r, sx, sy)
  love.graphics.push()
  love.graphics.translate(x or 0, y or 0)
  love.graphics.scale(sx or 1, sy or sx or 1) --传入的self.spring.x用于缩放
  love.graphics.rotate(r or 0)
  love.graphics.translate(-x or 0, -y or 0)
end
```
可以看到传入的self.spring.x用于缩放，意思是GameObject中的self.spring是控制GameObject实例变大变小的那种弹簧。
用Spring.x改变坐标系是《SNKRX》的用法。也可以像笔者的实验代码中那样，直接用Spring.x来改变所绘制object的比例：
```Lua
  love.graphics.print("Daomoru Hit effects example!", gw/2, gh/2, 0, self.text_spring.x, self.text_spring.x, self.w/2, self.h/2)
end
```

### HitFx类
Springs类是Spring类的集合，通常和Flashes类配合在一起用，由HitFX类统一管理，不单独使用。一般来说，不需要闪烁效果只需要弹簧效果时，就用self.spring；既需要闪烁效果也需要弹簧效果时，就用self.hfx。
`self.hfx = HitFX(self)`这行代码将self注入到self.hfx中。self.hfx统一管理self.springs和self.flashes。再强调一次，self.springs和self.flashes不会单独使用。
来看一个例子，ForceField:draw()的代码：
```Lua
function ForceField:draw()
  if self.hidden then return end
  graphics.push(self.x, self.y, 0, self.spring.x*self.hfx.hit.x, self.spring.x*self.hfx.hit.x)
    graphics.circle(self.x, self.y, self.shape.rs, self.hfx.hit.f and fg[0] or self.color, 2)
    graphics.circle(self.x, self.y, self.shape.rs, self.hfx.hit.f and fg_transparent[0] or self.color_transparent)
  graphics.pop()
end
```
这里既用到了self.spring.x，也用到了self.hfx.hit.x。self.spring.pull(0.15)在ForceField:init(args)中调用，self.hfx:use('hit', 0.2)在ForceField:on_collision_enter(other, contact)中调用。

Flashes和Springs的文档还可以看这里：https://github.com/a327ex/emoji-merge?tab=readme-ov-file#hitfx-flashes-and-springs
## 10.4、粒子效果
粒子效果由shared.lua中的HitParticle和HitCircle实现。笔者在mainmenu.lua的MainMenu:on_enter(from)内放了实验用代码，读者可以取消注释，看看HitParticle和HitCircle是什么样子的。
## 10.5、慢动作效果
打开shared.lua文件夹，有个全局函数slow(amount, duration, tween_method)：
```Lua
function slow(amount, duration, tween_method)
  amount = amount or 0.5
  duration = duration or 0.5
  tween_method = tween_method or math.cubic_in_out
  slow_amount = amount
  trigger:tween(duration, _G, {slow_amount = 1}, tween_method, function() slow_amount = 1 end, 'slow')
end
```
这便是产生慢动作效果的函数。
该函数控制slow_amount这个全局变量。将slow_amount乘以Group:update(dt)的参数dt，即可使该Group变慢1/slow_amount倍。
例如Arena:update(dt)函数定义中：
```Lua
  self:update_game_object(dt*slow_amount)
  main_song_instance.pitch = math.clamp(slow_amount*music_slow_amount, 0.05, 1)
  
  star_group:update(dt*slow_amount)
  self.floor:update(dt*slow_amount)
  self.main:update(dt*slow_amount*self.main_slow_amount)
  self.post_main:update(dt*slow_amount)
  self.effects:update(dt*slow_amount)
  self.ui:update(dt*slow_amount)
  self.credits:update(dt)
```
可以看到，需要实现慢动作效果的Group的update(dt)的参数dt都乘以了slow_amount。
全局搜索“slow(”即可看到哪些地方调用了slow(amount, duration, tween_method)。
例如Player:hit(damage, from_undead)方法中：
```Lua
  if self.hp <= 0 then
    if self.divined then
      self:heal(self.max_hp)
      heal1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
      buff1:play{pitch = random:float(0.95, 1.05), volume = 0.5}
      for i = 1, random:int(4, 6) do HitParticle{group = main.current.effects, x = self.x, y = self.y, color = self.color} end
      HitCircle{group = main.current.effects, x = self.x, y = self.y, rs = 12}:scale_down(0.3):change_color(0.5, self.color)
      self.divined = false
    --passive:'第七个英雄在死亡后的十秒内仍然存活'
    elseif self.lasting_7 and self.follower_index == 6 and not self.undead then
      self.undead = true
      self.t:after(10, function() self:hit(10000, true) end)
    else
      hit4:play{pitch = random:float(0.95, 1.05), volume = 0.5}
      slow(0.25, 1) -- 有一个英雄死亡时，画面变慢至4倍，然后逐渐恢复到常速，这个过程持续1秒。
      for i = 1, random:int(4, 6) do HitParticle{group = main.current.effects, x = self.x, y = self.y, color = self.color} end
```
这是在有一个英雄死亡时，画面变慢4倍，持续1秒。不过要注意的是，这1秒内，不是恒定的慢4倍，而是逐渐恢复到常速。
## 10.6、汇总
对于以上几种打击感效果，笔者在mainmanu.lua里放了实验用的代码，请根据注释来做实验：
```Lua
local daomoruHfxExample = Object:extend()
daomoruHfxExample:implement(GameObject) --实现GameObject只是为了方便将daomoruHfxExample的实例放进一个Group里，并没有用到GameObject里的Spring、Spings、Flashes, HitFX等。
function daomoruHfxExample:init(args)
  self:init_game_object(args)
  self.text_spring = Spring(1)
  self.w, self.h = 100, 70
  self.daomoruFlashes = Flashes() --GameObject内已有Flashes实例的属性，为了区分，前面加个"daomoru"前缀。
  self.daomoruFlashes:add('hit')
end

function daomoruHfxExample:update(dt)
  self:update_game_object(dt)
  self.text_spring:update(dt)
  self.daomoruFlashes:update(dt)
  if input.f.pressed then --按f键触发效果。
    if not self.hot then
      self.hot = true
    --请依次解除下面几行注释，以分别查看弹簧效果，两种Camera抖动效果、闪烁效果、慢动作效果。
    --   self.text_spring:pull(0.25)
    --   camera:shake(4, 0.5)
    --   camera:spring_shake(20, math.pi - 10) --可以看到蛇跟文字一起抖向一边，而其他的按键UI不会动。
    --   self.daomoruFlashes.hit:flash(0.5)
    --   slow(0.1, 2)
    end
  else self.hot = false end
end

function daomoruHfxExample:draw()
  love.graphics.setColor(self.daomoruFlashes.hit.f and 1 or 0, 1, 1, 1) -- Flashes闪烁效果在这里实现。
  love.graphics.print("Daomoru Hit effects example!", gw/2, gh/2, 0, self.text_spring.x, self.text_spring.x, self.w/2, self.h/2)
end
```

```Lua
--看看摄相机抖动、Flashes、Spring以及慢动作是什么效果。
--   daomoruHfxExample{group = self.main}

--以下两项是粒子效果。
--看看HitParticle是什么样子。
--   self.t:every(2,function()
--     HitParticle{group = self.main, x = 250, y = 150, color = red[0]}
--     HitParticle{group = self.main, x = 250, y = 150, color = red[0]}
--     HitParticle{group = self.main, x = 250, y = 150, color = red[0]}
--     HitParticle{group = self.main, x = 250, y = 150, color = red[0]}
--     HitParticle{group = self.main, x = 250, y = 150, color = red[0]}
--     HitParticle{group = self.main, x = 250, y = 150, color = red[0]}
--   end, 5)

--看看HitCircle是什么样子。
--   self.t:every(2, function()
--     HitCircle{group = self.main, x = 250, y = 150, rs = 6, color = orange[0], duration = 1.5}
--   end, 5)
```
打击感的效果还有很多，例如Physics:wander(rs, distance, jitter, weight)这个函数可以使物体呈现哆哆嗦嗦的样子，以及非常重要的音效。这些笔者以后可能会补充。
# 十一、特效文字
本节讲述《SNKRX》中如何实现特效文字，例如mainmenu中左上角波浪形跳动的“SNKRX”。
首先来看shared.lua中定义的global_text_tags这个table，里面定义了各种特效文字的更新函数draw、以及绘制函数update，例如波浪形跳动的tag就是以“wavy”开头的几个tag：
```Lua
  wavy = TextTag{update = function(c, dt, i, text) c.oy = 2*math.sin(4*time + i) end}, --文字波浪形逐个跳动，举例：mainmenu场景左上角大大的SNKRX。
  wavy_mid = TextTag{update = function(c, dt, i, text) c.oy = 0.75*math.sin(3*time + i) end},
  wavy_mid2 = TextTag{update = function(c, dt, i, text) c.oy = 0.5*math.sin(3*time + i) end},
  wavy_lower = TextTag{update = function(c, dt, i, text) c.oy = 0.25*math.sin(2*time + i) end},
```
接下来看engine/graphics/text.lua文件，可以看到有详细注释。
定义Text类的实例时，将global_text_tags作为第二个参数传进去，第一个参数是包含将被绘制字符串的一个table。
文字特效的tag需要被放在中括号内。
我们来看特效文字的例子，buy_screen.lua文件中：
```Lua
  self.shop_text = Text({{text = '[wavy_mid, fg]shop [fg]- gold: [yellow]' .. gold, font = pixul_font, alignment = 'center'}}, global_text_tags)

  self.party_text = Text({{text = '[wavy_mid, fg]party ' .. tostring(#units) .. '/' .. tostring(max_units), font = pixul_font, alignment = 'center'}}, global_text_tags)

  self.sets_text = Text({{text = '[wavy_mid, fg]classes', font = pixul_font, alignment = 'center'}}, global_text_tags)

  self.items_text = Text({{text = '[wavy_mid, fg]items', font = pixul_font, alignment = 'center'}}, global_text_tags)
```
这四行分别是buy_screen场景中的几个栏目名：“shop”、“party”、“classes”以及“items”。它们都使用了“wavy_mid”、“fg”这两个tag。
Text类会解析出tag，然后：
在Text:set_text(text_data)中调用global_text_tags中对应tag定义的init(c, i, self)；
在Text:update(dt)中调用对应tag的update(c, dt, i, self)；
在Text:draw(x, y, r, sx, sy)中调用对应tag的draw(c, i, self)。
如此，便实现了特效文字。

特效文字的文档可以看这里：https://github.com/a327ex/emoji-merge?tab=readme-ov-file#text
# 十二、结语
《SNKRX》的engine框架的代码非常好，注释非常详细。Gameplay部分的代码虽然没什么注释，但因为游戏本身不复杂，所以也不难读。
如果您发现笔记中有错误，或者有疑问，请在Bilibili私信up主道莫如。
本笔记后续可能会有纠正和补充，请按“更新地址.txt”中的地址获取最新更新。

# 十三、love引擎启动命令
open -n -a love /Users/<USER>/Documents/Repository/SNKRX
open -n -a love /Users/<USER>/Documents/LoveProjects/DaomoruSNKRX_NoSteam_Chinese