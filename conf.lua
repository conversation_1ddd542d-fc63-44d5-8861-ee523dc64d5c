--[[
模块：conf.lua（Love2D 基础配置）
职责：
- Love2D 的全局配置入口，设置版本、窗口大小、垂直同步与抗锯齿等
关键点：
- t.version：运行所需的 Love2D 版本号（此处为 11.5）
- t.window.width/height：默认窗口分辨率（960x540），与 main.lua 的 engine_run 窗口策略配合
- t.window.vsync：开启垂直同步，保证画面稳定，避免撕裂
- t.window.msaa：多重采样抗锯齿开关（0 代表关闭）
性能与兼容：
- 低配设备建议维持较低分辨率与关闭 MSAA；高帧率需要平衡 vsync 与刷新率
]]--

function love.conf(t)
  t.version = "11.5"
  t.window.width = 960
  t.window.height = 540
  t.window.vsync = 1
  t.window.msaa = 0
end
