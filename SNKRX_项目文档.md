# SNKRX 项目深度分析文档

## 项目概述

**SNKRX** 是一款基于 LÖVE 2D 引擎开发的街机射击 Roguelite 游戏，使用 Lua 语言编写。游戏已在 Steam 平台发布，玩家控制一条由英雄组成的蛇，这些英雄会自动攻击附近的敌人。通过组合不同的英雄来解锁职业加成并创建独特的构建。

### 基本信息
- **游戏类型**: 街机射击 Roguelite
- **开发引擎**: LÖVE 2D (Love2D)
- **编程语言**: Lua
- **发布平台**: Steam
- **许可证**: MIT License (代码部分)
- **分辨率**: 960x540
- **垂直同步**: 开启

## 技术架构

### 1. 引擎架构 (engine/)

项目采用自定义的游戏引擎，基于 LÖVE 2D 构建，包含以下核心模块：

#### 数据结构模块 (datastructures/)
- **string.lua**: 字符串处理扩展
- **table.lua**: 表操作扩展
- **graph.lua**: 图数据结构
- **grid.lua**: 网格数据结构

#### 游戏核心系统 (game/)
- **gameobject.lua**: 游戏对象基类，实现基础的更新、绘制、事件处理
- **group.lua**: 对象组管理，负责批量更新和绘制
- **state.lua**: 状态管理系统，支持状态切换和生命周期管理
- **physics.lua**: 物理系统集成
- **input.lua**: 输入处理系统
- **trigger.lua**: 定时器和事件触发系统
- **steering.lua**: AI 导航和寻路系统

#### 图形系统 (graphics/)
- **graphics.lua**: 核心图形渲染
- **camera.lua**: 相机系统
- **animation.lua**: 动画系统
- **color.lua**: 颜色管理
- **font.lua**: 字体渲染
- **image.lua**: 图像处理
- **shader.lua**: 着色器支持

#### 数学库 (math/)
- **vector.lua**: 向量运算
- **circle.lua, rectangle.lua, polygon.lua**: 几何形状
- **random.lua**: 随机数生成
- **spring.lua**: 弹簧物理

#### 外部库 (external/)
- **binser.lua**: 二进制序列化
- **clipper.lua**: 多边形裁剪
- **mlib.lua**: 数学库扩展
- **ripple.lua**: 音频处理

### 2. 游戏状态管理

游戏采用状态机模式管理不同的游戏场景：

- **MainMenu**: 主菜单状态
- **Arena**: 战斗场景状态
- **BuyScreen**: 商店购买状态

状态切换通过 `main:go_to()` 方法实现，支持状态间的数据传递和生命周期管理。

### 3. 物理系统

基于 Love2D 的 Box2D 物理引擎：
- 支持动态、静态物体
- 碰撞检测和响应
- 触发器系统
- 物理分组和过滤

## 游戏系统设计

### 1. 角色系统

游戏包含 60+ 个独特角色，每个角色都有：

#### 角色属性
- **character**: 角色类型标识
- **level**: 角色等级 (1-3)
- **classes**: 所属职业类别
- **color**: 角色颜色标识

#### 核心属性
- **HP**: 生命值
- **DMG**: 伤害值
- **ASPD**: 攻击速度倍率
- **AREA**: 范围伤害倍率
- **DEF**: 防御值
- **MVSPD**: 移动速度

#### 角色分类示例
```lua
character_classes = {
  ['vagrant'] = {'explorer', 'psyker'},
  ['swordsman'] = {'warrior'},
  ['wizard'] = {'mage', 'nuker'},
  ['archer'] = {'ranger'},
  -- ... 更多角色
}
```

### 2. 职业系统

游戏包含 16 个职业类别，每个职业都有独特的颜色和加成效果：

- **Warrior** (黄色): 近战战士
- **Ranger** (绿色): 远程射手
- **Mage** (蓝色): 法师
- **Rogue** (红色): 刺客
- **Healer** (绿色): 治疗者
- **Nuker** (红色): 爆发输出
- **Conjurer** (橙色): 召唤师
- **Enchanter** (蓝色): 增益师
- **Psyker** (前景色): 精神系
- **Curser** (紫色): 诅咒师
- **Forcer** (黄色): 力场师
- **Swarmer** (橙色): 群体召唤
- **Voider** (紫色): 虚空系
- **Sorcerer** (蓝2): 术士
- **Mercenary** (黄2): 雇佣兵
- **Explorer** (前景色): 探索者

### 3. 战斗系统

#### 攻击机制
每个角色都有独特的攻击模式：
- **射击型**: 发射投射物攻击敌人
- **近战型**: 近距离范围攻击
- **法术型**: 魔法区域攻击
- **召唤型**: 召唤单位或陷阱

#### 伤害计算
```lua
-- 防御计算公式
if defense >= 0 then 
  dmg_m = 100/(100+defense) 
else 
  dmg_m = 2-100/(100-defense)
end
```

### 4. 敌人系统

#### 敌人类型
- **Seeker**: 基础追击敌人
- **EnemyCritter**: 小型敌人单位
- **Boss**: 精英敌人，具有特殊能力

#### AI 行为
- 寻路和追击玩家
- 避障和分离行为
- 攻击模式和技能释放

### 5. 物品和被动技能系统

游戏包含大量被动技能，提供各种增益效果：
- 攻击强化类
- 防御增强类
- 特殊效果类
- 经济增益类

## 资源管理

### 1. 音频系统
- **音效**: 100+ 种游戏音效
- **音乐**: 5 首背景音乐 + 死亡音乐
- **动态音量**: 基于游戏状态调整

### 2. 图像资源
- **角色图标**: 每个角色的视觉表示
- **技能图标**: 被动技能的图标
- **UI 元素**: 界面组件图像

### 3. 数据持久化
- **游戏状态**: 使用 `state.txt` 保存
- **运行数据**: 使用 `run_v4.txt` 保存当前游戏进度
- **Steam 集成**: 成就和统计数据同步

## 开发特色

### 1. 模块化设计
- 清晰的模块分离
- 可复用的组件系统
- 灵活的扩展机制

### 2. 数据驱动
- 角色数据通过表格配置
- 技能效果参数化
- 平衡性调整便捷

### 3. 性能优化
- 对象池管理
- 高效的碰撞检测
- 内存使用监控

### 4. 开发工具
- 实时调试信息
- 内存使用统计
- 类型计数工具

## 构建和部署

### 运行要求
- LÖVE 2D 11.3+
- Steam 客户端 (完整功能)
- Windows/Linux/macOS 支持

### 构建脚本
- `build.sh`: Linux/macOS 构建
- `run.sh`: 快速运行脚本
- `engine/love/build_*.bat`: Windows 构建脚本

## 项目亮点

1. **完整的商业游戏**: 已在 Steam 发布的完整产品
2. **自定义引擎**: 基于 LÖVE 2D 的定制化游戏引擎
3. **复杂的游戏系统**: 深度的角色、职业、技能系统
4. **优秀的代码组织**: 清晰的模块化架构
5. **丰富的内容**: 60+ 角色，100+ 技能，多样化的游戏机制
6. **性能优化**: 针对实时游戏的性能优化
7. **跨平台支持**: 支持多个操作系统平台

这个项目展示了如何使用 Lua 和 LÖVE 2D 创建一个功能完整、性能优良的商业游戏，是学习游戏开发的优秀范例。
